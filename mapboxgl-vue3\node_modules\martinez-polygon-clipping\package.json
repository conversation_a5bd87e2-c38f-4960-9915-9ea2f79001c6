{"name": "martinez-polygon-clipping", "version": "0.7.4", "description": "<PERSON> polygon clipping algorithm, does boolean operation on polygons (multipolygons, polygons with holes etc): intersection, union, difference, xor", "main": "dist/martinez.umd.js", "browser": "dist/martinez.umd.js", "module": "index.js", "jsnext:main": "index.js", "types": "index.d.ts", "files": ["index.d.ts", "index.js", "src/", "dist/"], "scripts": {"test": "tape -r reify buble/register test/*.test.js | tap-spec && npm run ts", "ts": "tsc index.d.ts", "bench": "node --require reify bench.js", "lint": "eslint ./src/", "watch": "rollup -c -w", "serve": "http-server -p 3000", "start": "run-p watch serve", "build": "npm run lint && rollup -c && npm run min", "min": "uglifyjs ./dist/martinez.umd.js -o ./dist/martinez.min.js -m --comments", "prepublishOnly": "npm run lint && npm run test && npm run build"}, "keywords": ["polygon", "clipping", "boolean", "intersection", "union", "xor", "geometry", "mart<PERSON>z"], "repository": {"url": "git+https://github.com/w8r/martinez.git", "type": "git"}, "homepage": "https://github.com/w8r/martinez", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@turf/union": "^4.6.0", "@types/geojson": "^1.0.2", "benchmark": "^2.1.4", "buble": "^0.19.3", "eslint": "^6.8.0", "eslint-plugin-import-order": "^2.1.4", "eslint-plugin-jsdoc": "^20.0.2", "eslint-plugin-promise": "^3.8.0", "geojson-project": "^1.0.0", "http-server": "^0.12.1", "json-stringify-pretty-compact": "^2.0.0", "leaflet": "^1.2.0", "leaflet-editable": "^1.1.0", "load-json-file": "^2.0.0", "npm-run-all": "^4.1.5", "reify": "^0.20.12", "rollup": "^1.28.0", "rollup-plugin-buble": "^0.19.8", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "tap-spec": "^5.0.0", "tap-status": "^1.0.1", "tape": "^4.12.1", "typescript": "^3.7.4", "uglify-js": "^3.3.21"}, "dependencies": {"robust-predicates": "^2.0.4", "splaytree": "^0.1.4", "tinyqueue": "^1.2.0"}}