# MapboxGL Vue3 项目文档

基于 maps.suunto.com 项目分析，使用 Vue3 + Vite + ElementPlus + Pinia 技术栈实现核心功能的1:1复刻。

## 文档目录

### 核心文档
- **[功能清单.md](./功能清单.md)** - 完整的功能清单和技术架构组件
- **[系统架构图.md](./系统架构图.md)** - 系统架构图（Mermaid格式）
- **[核心功能流程图.md](./核心功能流程图.md)** - 核心功能流程图（Mermaid格式）

## 项目概述

本项目旨在使用Vue3技术栈复刻Suunto地图应用的核心功能，专注于：

1. **WebGL高性能轨迹渲染** - 自定义着色器，支持10000+数据点
2. **3D地形系统** - 使用地形方差图（蓝色通道）设置高程
3. **3D相机动画系统** - Catmull-Rom样条插值，电影级相机运动
4. **轨迹数据处理** - GPS解析，数据优化算法
5. **基础动画控制** - 40秒循环，播放/暂停控制

## 技术架构

### 核心技术栈
- **Vue 3.4+** - 前端框架
- **Vite 5+** - 构建工具  
- **Pinia 2+** - 状态管理
- **TypeScript 5+** - 类型系统
- **Mapbox GL JS 3+** - 地图引擎

### 架构层次
1. **用户界面层** - Vue3组件
2. **状态管理层** - Pinia stores
3. **组合式API层** - Composables
4. **核心服务层** - Services
5. **算法核心层** - Utils

## 核心功能优先级

### 第一优先级 - Mapbox地图集成（3D地形支持）
- Vue3组件中集成Mapbox GL JS
- 地形方差图集成（蓝色通道高程）
- 地形夸张度动态调整
- DEM数据源配置

### 第二优先级 - 3D相机动画系统
- 移植React的CatmullRomSpline核心代码
- 向心参数化算法实现
- 球坐标系相机定位
- 电影级相机运动

### 第三优先级 - 轨迹数据处理
- GPS坐标解析和格式化
- Douglas-Peucker轨迹简化算法
- 轨迹点插值和数据优化
- 时间同步和数据对齐

### 第四优先级 - 基础动画控制
- usePosition composable实现
- 40秒固定时长动画循环
- 播放/暂停状态管理
- requestAnimationFrame驱动

### 第五优先级 - 自定义图层
- 参考原有LineLayer实现
- WebGL自定义着色器
- 批处理数据更新系统
- 高性能渲染优化

## 项目结构

```
mapboxgl-vue3/
├── docs/                    # 项目文档
│   ├── README.md           # 文档总览
│   ├── 功能清单.md          # 详细功能清单
│   ├── 系统架构图.md        # 系统架构图（Mermaid）
│   └── 核心功能流程图.md    # 功能流程图（Mermaid）
├── src/
│   ├── components/         # Vue组件
│   ├── composables/        # 组合式API
│   ├── stores/            # Pinia状态管理
│   ├── services/          # 核心服务
│   ├── utils/             # 工具函数
│   └── types/             # TypeScript类型
└── ...
```

## 开发指南

### 查看架构图
1. 架构图使用Mermaid语法编写，可在GitHub、VS Code等支持Mermaid的环境中直接查看
2. 系统架构图展示了5层架构的组件关系和数据流向
3. 核心功能流程图展示了从应用启动到WebGL渲染的完整流程，包含5个主要阶段

### 功能实现顺序
按照优先级顺序实现功能，确保每个阶段都有可运行的版本：
1. 先实现Mapbox地图集成和3D地形
2. 移植3D相机动画系统
3. 完成轨迹数据处理
4. 实现动画控制系统
5. 最后完成WebGL自定义渲染

## 成功标准

### 性能目标
- 60FPS流畅动画
- 支持10000+轨迹点渲染
- 内存使用优化

### 功能完整性
- 完整的3D地形渲染
- 平滑的相机动画
- 高性能轨迹渲染
- 精确的动画控制

### 代码质量
- TypeScript完整类型支持
- 模块化架构设计
- 性能优化实现

## 参考资料

基于对 maps.suunto.com 项目的深入分析：
- **WebGL LineLayer**: 自定义着色器 + 批处理系统
- **3D相机系统**: Catmull-Rom样条插值 + 电影级动画
- **智能地形系统**: 动态DEM数据源 + 自适应夸张度
- **40秒动画循环**: requestAnimationFrame + 精确时间计算


