/**
 * 数据服务 - 模拟API调用，从本地文件加载数据
 */

import type { WorkoutData, TrackPoint, SensorData } from '@/types/track'
import type { ApiResponse } from '@/types/api'

export class DataService {
  private static instance: DataService
  private cache = new Map<string, any>()

  static getInstance(): DataService {
    if (!DataService.instance) {
      DataService.instance = new DataService()
    }
    return DataService.instance
  }

  /**
   * 加载运动数据响应
   */
  async loadWorkoutResponse(): Promise<any> {
    const cacheKey = 'workout-response'

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const response = await fetch('/mock-data/response.json')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      this.cache.set(cacheKey, data)
      return data
    } catch (error) {
      console.error('Failed to load workout response:', error)
      // 返回模拟数据作为fallback
      const fallbackData = {
        workout: {
          fullname: 'Test User',
          startTime: Date.now() - 2400000, // 40分钟前
          totalTime: 2400, // 40分钟
          totalDistance: 8500, // 8.5km
          avgPace: 4.7, // 4:42 min/km
          hrdata: {
            avg: 162,
            max: 185
          },
          energyConsumption: 650,
          recoveryTime: 64800, // 18小时
          extensions: [
            {
              type: 'FitnessExtension',
              estimatedVo2Max: 45.2
            }
          ]
        }
      }
      this.cache.set(cacheKey, fallbackData)
      return fallbackData
    }
  }

  /**
   * 加载轨迹坐标点 (GeoJSON格式)
   */
  async loadTrackPoints(): Promise<GeoJSON.FeatureCollection> {
    const cacheKey = 'track-points'

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const response = await fetch('/geo-data/workout_track.geojson')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      this.cache.set(cacheKey, data)
      return data
    } catch (error) {
      console.error('Failed to load track points:', error)
      throw new Error('Failed to load track points')
    }
  }

  /**
   * 加载CSV格式的轨迹数据
   */
  async loadTrackCSV(): Promise<string> {
    const cacheKey = 'track-csv'

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const response = await fetch('/geo-data/workout_track.csv')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.text()
      this.cache.set(cacheKey, data)
      return data
    } catch (error) {
      console.error('Failed to load track CSV:', error)
      throw new Error('Failed to load track CSV')
    }
  }

  /**
   * 加载GPX格式的轨迹数据
   */
  async loadTrackGPX(): Promise<string> {
    const cacheKey = 'track-gpx'

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)
    }

    try {
      const response = await fetch('/geo-data/workout_track.gpx')
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.text()
      this.cache.set(cacheKey, data)
      return data
    } catch (error) {
      console.error('Failed to load track GPX:', error)
      throw new Error('Failed to load track GPX')
    }
  }

  /**
   * 解析运动数据，提取核心信息
   */
  async getWorkoutData(): Promise<WorkoutData> {
    const response = await this.loadWorkoutResponse()

    return {
      id: response.data.workout.id,
      name: response.data.workout.name,
      type: response.data.workout.type,
      date: response.data.workout.date,
      duration: response.data.workout.duration,
      distance: response.data.workout.distance,
      elevationGain: response.data.workout.elevationGain,
      elevationLoss: response.data.workout.elevationLoss,
      startLocation: response.data.workout.startLocation,
      endLocation: response.data.workout.endLocation,
      animation: response.data.animation,
      rendering: response.data.rendering,
      terrain: response.data.terrain
    }
  }

  /**
   * 获取传感器数据（从response.json中提取）
   */
  async getSensorData(): Promise<SensorData[]> {
    const response = await this.loadWorkoutResponse()
    const workout = response.workout

    if (!workout.extensions) {
      return []
    }

    const sensorData: SensorData[] = []

    // 提取高程数据
    const altitudeExt = workout.extensions.find((ext: any) => ext.type === 'AltitudeStreamExtension')
    if (altitudeExt) {
      altitudeExt.values.forEach((value: number, index: number) => {
        sensorData.push({
          timestamp: altitudeExt.timestamps[index],
          type: 'altitude',
          value: value
        })
      })
    }

    // 提取心率数据
    const hrExt = workout.extensions.find((ext: any) => ext.type === 'HeartrateStreamExtension')
    if (hrExt) {
      hrExt.values.forEach((value: number, index: number) => {
        sensorData.push({
          timestamp: hrExt.timestamps[index],
          type: 'heartrate',
          value: value
        })
      })
    }

    return sensorData
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * 获取缓存状态
   */
  getCacheInfo(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}
