"use strict";const INDENT_REGEX=/^(?:( )+|\t+)/,INDENT_TYPE_SPACE="space",INDENT_TYPE_TAB="tab";function makeIndentsMap(e,t){const n=new Map;let i=0,a,c;for(const f of e.split(/\n/g)){if(!f)continue;let l,u,y,m,d;const h=f.match(INDENT_REGEX);if(h===null)i=0,a="";else{if(l=h[0].length,u=h[1]?INDENT_TYPE_SPACE:INDENT_TYPE_TAB,t&&u===INDENT_TYPE_SPACE&&l===1)continue;u!==a&&(i=0),a=u,y=1,m=0;const p=l-i;if(i=l,p===0)y=0,m=1;else{const g=p>0?p:-p;c=encodeIndentsKey(u,g)}d=n.get(c),d=d===void 0?[1,0]:[d[0]+y,d[1]+m],n.set(c,d)}}return n}function encodeIndentsKey(e,t){return(e===INDENT_TYPE_SPACE?"s":"t")+String(t)}function decodeIndentsKey(e){const n=e[0]==="s"?INDENT_TYPE_SPACE:INDENT_TYPE_TAB,i=Number(e.slice(1));return{type:n,amount:i}}function getMostUsedKey(e){let t,n=0,i=0;for(const[a,[c,f]]of e)(c>n||c===n&&f>i)&&(n=c,i=f,t=a);return t}function makeIndentString(e,t){return(e===INDENT_TYPE_SPACE?" ":"	").repeat(t)}function detectIndent(e){if(typeof e!="string")throw new TypeError("Expected a string");let t=makeIndentsMap(e,!0);t.size===0&&(t=makeIndentsMap(e,!1));const n=getMostUsedKey(t);let i,a=0,c="";return n!==void 0&&({type:i,amount:a}=decodeIndentsKey(n),c=makeIndentString(i,a)),{amount:a,type:i,indent:c}}const r=Symbol.for("__confbox_fmt__"),o=/^(\s+)/,s=/(\s+)$/;function detectFormat(e,t={}){const n=t.indent===void 0&&t.preserveIndentation!==!1&&e.slice(0,t?.sampleSize||1024),i=t.preserveWhitespace===!1?void 0:{start:o.exec(e)?.[0]||"",end:s.exec(e)?.[0]||""};return{sample:n,whiteSpace:i}}function storeFormat(e,t,n){!t||typeof t!="object"||Object.defineProperty(t,r,{enumerable:!1,configurable:!0,writable:!0,value:detectFormat(e,n)})}function getFormat(e,t){if(!e||typeof e!="object"||!(r in e))return{indent:t?.indent,whitespace:{start:"",end:""}};const n=e[r];return{indent:t?.indent||detectIndent(n.sample||"").indent,whitespace:n.whiteSpace||{start:"",end:""}}}exports.getFormat=getFormat,exports.storeFormat=storeFormat;
