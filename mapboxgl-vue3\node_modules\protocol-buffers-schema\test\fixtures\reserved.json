{"syntax": 3, "package": null, "imports": [], "enums": [{"name": "ReservedEnum", "values": {"x": {"value": 1, "options": []}, "y": {"value": 10, "options": []}}, "options": {}}], "messages": [{"name": "Reserved", "extensions": null, "enums": [], "extends": [], "messages": [], "options": {}, "fields": [{"name": "x", "type": "string", "tag": 1, "map": null, "oneof": null, "required": false, "repeated": false, "options": {}}, {"name": "y", "type": "string", "tag": 10, "map": null, "oneof": null, "required": false, "repeated": false, "options": {}}]}], "options": {}, "extends": []}