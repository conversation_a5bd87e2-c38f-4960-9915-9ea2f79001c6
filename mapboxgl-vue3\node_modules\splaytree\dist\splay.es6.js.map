{"version": 3, "file": "splay.es6.js", "sources": ["../index.js"], "sourcesContent": ["function DEFAULT_COMPARE (a, b) { return a > b ? 1 : a < b ? -1 : 0; }\n\nexport default class SplayTree {\n\n  constructor(compare = DEFAULT_COMPARE, noDuplicates = false) {\n    this._compare = compare;\n    this._root = null;\n    this._size = 0;\n    this._noDuplicates = !!noDuplicates;\n  }\n\n\n  rotateLeft(x) {\n    var y = x.right;\n    if (y) {\n      x.right = y.left;\n      if (y.left) y.left.parent = x;\n      y.parent = x.parent;\n    }\n\n    if (!x.parent)                this._root = y;\n    else if (x === x.parent.left) x.parent.left = y;\n    else                          x.parent.right = y;\n    if (y) y.left = x;\n    x.parent = y;\n  }\n\n\n  rotateRight(x) {\n    var y = x.left;\n    if (y) {\n      x.left = y.right;\n      if (y.right) y.right.parent = x;\n      y.parent = x.parent;\n    }\n\n    if (!x.parent)               this._root = y;\n    else if(x === x.parent.left) x.parent.left = y;\n    else                         x.parent.right = y;\n    if (y) y.right = x;\n    x.parent = y;\n  }\n\n\n  _splay(x) {\n    while (x.parent) {\n      var p = x.parent;\n      if (!p.parent) {\n        if (p.left === x) this.rotateRight(p);\n        else              this.rotateLeft(p);\n      } else if (p.left === x && p.parent.left === p) {\n        this.rotateRight(p.parent);\n        this.rotateRight(p);\n      } else if (p.right === x && p.parent.right === p) {\n        this.rotateLeft(p.parent);\n        this.rotateLeft(p);\n      } else if (p.left === x && p.parent.right === p) {\n        this.rotateRight(p);\n        this.rotateLeft(p);\n      } else {\n        this.rotateLeft(p);\n        this.rotateRight(p);\n      }\n    }\n  }\n\n\n  splay(x) {\n    var p, gp, ggp, l, r;\n\n    while (x.parent) {\n      p = x.parent;\n      gp = p.parent;\n\n      if (gp && gp.parent) {\n        ggp = gp.parent;\n        if (ggp.left === gp) ggp.left  = x;\n        else                 ggp.right = x;\n        x.parent = ggp;\n      } else {\n        x.parent = null;\n        this._root = x;\n      }\n\n      l = x.left; r = x.right;\n\n      if (x === p.left) { // left\n        if (gp) {\n          if (gp.left === p) {\n            /* zig-zig */\n            if (p.right) {\n              gp.left = p.right;\n              gp.left.parent = gp;\n            } else gp.left = null;\n\n            p.right   = gp;\n            gp.parent = p;\n          } else {\n            /* zig-zag */\n            if (l) {\n              gp.right = l;\n              l.parent = gp;\n            } else gp.right = null;\n\n            x.left    = gp;\n            gp.parent = x;\n          }\n        }\n        if (r) {\n          p.left = r;\n          r.parent = p;\n        } else p.left = null;\n\n        x.right  = p;\n        p.parent = x;\n      } else { // right\n        if (gp) {\n          if (gp.right === p) {\n            /* zig-zig */\n            if (p.left) {\n              gp.right = p.left;\n              gp.right.parent = gp;\n            } else gp.right = null;\n\n            p.left = gp;\n            gp.parent = p;\n          } else {\n            /* zig-zag */\n            if (r) {\n              gp.left = r;\n              r.parent = gp;\n            } else gp.left = null;\n\n            x.right   = gp;\n            gp.parent = x;\n          }\n        }\n        if (l) {\n          p.right = l;\n          l.parent = p;\n        } else p.right = null;\n\n        x.left   = p;\n        p.parent = x;\n      }\n    }\n  }\n\n\n  replace(u, v) {\n    if (!u.parent) this._root = v;\n    else if (u === u.parent.left) u.parent.left = v;\n    else u.parent.right = v;\n    if (v) v.parent = u.parent;\n  }\n\n\n  minNode(u = this._root) {\n    if (u) while (u.left) u = u.left;\n    return u;\n  }\n\n\n  maxNode(u = this._root) {\n    if (u) while (u.right) u = u.right;\n    return u;\n  }\n\n\n  insert(key, data) {\n    var z = this._root;\n    var p = null;\n    var comp = this._compare;\n    var cmp;\n\n    if (this._noDuplicates) {\n      while (z) {\n        p = z;\n        cmp = comp(z.key, key);\n        if (cmp === 0) return;\n        else if (comp(z.key, key) < 0) z = z.right;\n        else z = z.left;\n      }\n    } else {\n      while (z) {\n        p = z;\n        if (comp(z.key, key) < 0) z = z.right;\n        else z = z.left;\n      }\n    }\n\n    z = { key, data, left: null, right: null, parent: p };\n\n    if (!p)                          this._root = z;\n    else if (comp(p.key, z.key) < 0) p.right = z;\n    else                             p.left  = z;\n\n    this.splay(z);\n    this._size++;\n    return z;\n  }\n\n\n  find (key) {\n    var z    = this._root;\n    var comp = this._compare;\n    while (z) {\n      var cmp = comp(z.key, key);\n      if      (cmp < 0) z = z.right;\n      else if (cmp > 0) z = z.left;\n      else              return z;\n    }\n    return null;\n  }\n\n  /**\n   * Whether the tree contains a node with the given key\n   * @param  {Key} key\n   * @return {boolean} true/false\n   */\n  contains (key) {\n    var node       = this._root;\n    var comparator = this._compare;\n    while (node)  {\n      var cmp = comparator(key, node.key);\n      if      (cmp === 0) return true;\n      else if (cmp < 0)   node = node.left;\n      else                node = node.right;\n    }\n\n    return false;\n  }\n\n\n  remove (key) {\n    var z = this.find(key);\n\n    if (!z) return false;\n\n    this.splay(z);\n\n    if (!z.left) this.replace(z, z.right);\n    else if (!z.right) this.replace(z, z.left);\n    else {\n      var y = this.minNode(z.right);\n      if (y.parent !== z) {\n        this.replace(y, y.right);\n        y.right = z.right;\n        y.right.parent = y;\n      }\n      this.replace(z, y);\n      y.left = z.left;\n      y.left.parent = y;\n    }\n\n    this._size--;\n    return true;\n  }\n\n\n  removeNode(z) {\n    if (!z) return false;\n\n    this.splay(z);\n\n    if (!z.left) this.replace(z, z.right);\n    else if (!z.right) this.replace(z, z.left);\n    else {\n      var y = this.minNode(z.right);\n      if (y.parent !== z) {\n        this.replace(y, y.right);\n        y.right = z.right;\n        y.right.parent = y;\n      }\n      this.replace(z, y);\n      y.left = z.left;\n      y.left.parent = y;\n    }\n\n    this._size--;\n    return true;\n  }\n\n\n  erase (key) {\n    var z = this.find(key);\n    if (!z) return;\n\n    this.splay(z);\n\n    var s = z.left;\n    var t = z.right;\n\n    var sMax = null;\n    if (s) {\n      s.parent = null;\n      sMax = this.maxNode(s);\n      this.splay(sMax);\n      this._root = sMax;\n    }\n    if (t) {\n      if (s) sMax.right = t;\n      else   this._root = t;\n      t.parent = sMax;\n    }\n\n    this._size--;\n  }\n\n  /**\n   * Removes and returns the node with smallest key\n   * @return {?Node}\n   */\n  pop () {\n    var node = this._root, returnValue = null;\n    if (node) {\n      while (node.left) node = node.left;\n      returnValue = { key: node.key, data: node.data };\n      this.remove(node.key);\n    }\n    return returnValue;\n  }\n\n\n  /* eslint-disable class-methods-use-this */\n\n  /**\n   * Successor node\n   * @param  {Node} node\n   * @return {?Node}\n   */\n  next (node) {\n    var successor = node;\n    if (successor) {\n      if (successor.right) {\n        successor = successor.right;\n        while (successor && successor.left) successor = successor.left;\n      } else {\n        successor = node.parent;\n        while (successor && successor.right === node) {\n          node = successor; successor = successor.parent;\n        }\n      }\n    }\n    return successor;\n  }\n\n\n  /**\n   * Predecessor node\n   * @param  {Node} node\n   * @return {?Node}\n   */\n  prev (node) {\n    var predecessor = node;\n    if (predecessor) {\n      if (predecessor.left) {\n        predecessor = predecessor.left;\n        while (predecessor && predecessor.right) predecessor = predecessor.right;\n      } else {\n        predecessor = node.parent;\n        while (predecessor && predecessor.left === node) {\n          node = predecessor;\n          predecessor = predecessor.parent;\n        }\n      }\n    }\n    return predecessor;\n  }\n  /* eslint-enable class-methods-use-this */\n\n\n  /**\n   * @param  {forEachCallback} callback\n   * @return {SplayTree}\n   */\n  forEach(callback) {\n    var current = this._root;\n    var s = [], done = false, i = 0;\n\n    while (!done) {\n      // Reach the left most Node of the current Node\n      if (current) {\n        // Place pointer to a tree node on the stack\n        // before traversing the node's left subtree\n        s.push(current);\n        current = current.left;\n      } else {\n        // BackTrack from the empty subtree and visit the Node\n        // at the top of the stack; however, if the stack is\n        // empty you are done\n        if (s.length > 0) {\n          current = s.pop();\n          callback(current, i++);\n\n          // We have visited the node and its left\n          // subtree. Now, it's right subtree's turn\n          current = current.right;\n        } else done = true;\n      }\n    }\n    return this;\n  }\n\n\n  /**\n   * Walk key range from `low` to `high`. Stops if `fn` returns a value.\n   * @param  {Key}      low\n   * @param  {Key}      high\n   * @param  {Function} fn\n   * @param  {*?}       ctx\n   * @return {SplayTree}\n   */\n  range(low, high, fn, ctx) {\n    const Q = [];\n    const compare = this._compare;\n    let node = this._root, cmp;\n\n    while (Q.length !== 0 || node) {\n      if (node) {\n        Q.push(node);\n        node = node.left;\n      } else {\n        node = Q.pop();\n        cmp = compare(node.key, high);\n        if (cmp > 0) {\n          break;\n        } else if (compare(node.key, low) >= 0) {\n          if (fn.call(ctx, node)) return this; // stop if smth is returned\n        }\n        node = node.right;\n      }\n    }\n    return this;\n  }\n\n  /**\n   * Returns all keys in order\n   * @return {Array<Key>}\n   */\n  keys () {\n    var current = this._root;\n    var s = [], r = [], done = false;\n\n    while (!done) {\n      if (current) {\n        s.push(current);\n        current = current.left;\n      } else {\n        if (s.length > 0) {\n          current = s.pop();\n          r.push(current.key);\n          current = current.right;\n        } else done = true;\n      }\n    }\n    return r;\n  }\n\n\n  /**\n   * Returns `data` fields of all nodes in order.\n   * @return {Array<Value>}\n   */\n  values () {\n    var current = this._root;\n    var s = [], r = [], done = false;\n\n    while (!done) {\n      if (current) {\n        s.push(current);\n        current = current.left;\n      } else {\n        if (s.length > 0) {\n          current = s.pop();\n          r.push(current.data);\n          current = current.right;\n        } else done = true;\n      }\n    }\n    return r;\n  }\n\n\n  /**\n   * Returns node at given index\n   * @param  {number} index\n   * @return {?Node}\n   */\n  at (index) {\n    // removed after a consideration, more misleading than useful\n    // index = index % this.size;\n    // if (index < 0) index = this.size - index;\n\n    var current = this._root;\n    var s = [], done = false, i = 0;\n\n    while (!done) {\n      if (current) {\n        s.push(current);\n        current = current.left;\n      } else {\n        if (s.length > 0) {\n          current = s.pop();\n          if (i === index) return current;\n          i++;\n          current = current.right;\n        } else done = true;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Bulk-load items. Both array have to be same size\n   * @param  {Array<Key>}    keys\n   * @param  {Array<Value>}  [values]\n   * @param  {Boolean}       [presort=false] Pre-sort keys and values, using\n   *                                         tree's comparator. Sorting is done\n   *                                         in-place\n   * @return {AVLTree}\n   */\n  load(keys = [], values = [], presort = false) {\n    if (this._size !== 0) throw new Error('bulk-load: tree is not empty');\n    const size = keys.length;\n    if (presort) sort(keys, values, 0, size - 1, this._compare);\n    this._root = loadRecursive(null, keys, values, 0, size);\n    this._size = size;\n    return this;\n  }\n\n\n  min() {\n    var node = this.minNode(this._root);\n    if (node) return node.key;\n    else      return null;\n  }\n\n\n  max() {\n    var node = this.maxNode(this._root);\n    if (node) return node.key;\n    else      return null;\n  }\n\n  isEmpty() { return this._root === null; }\n  get size() { return this._size; }\n\n\n  /**\n   * Create a tree and load it with items\n   * @param  {Array<Key>}          keys\n   * @param  {Array<Value>?}        [values]\n\n   * @param  {Function?}            [comparator]\n   * @param  {Boolean?}             [presort=false] Pre-sort keys and values, using\n   *                                               tree's comparator. Sorting is done\n   *                                               in-place\n   * @param  {Boolean?}             [noDuplicates=false]   Allow duplicates\n   * @return {SplayTree}\n   */\n  static createTree(keys, values, comparator, presort, noDuplicates) {\n    return new SplayTree(comparator, noDuplicates).load(keys, values, presort);\n  }\n}\n\n\nfunction loadRecursive (parent, keys, values, start, end) {\n  const size = end - start;\n  if (size > 0) {\n    const middle = start + Math.floor(size / 2);\n    const key    = keys[middle];\n    const data   = values[middle];\n    const node   = { key, data, parent };\n    node.left    = loadRecursive(node, keys, values, start, middle);\n    node.right   = loadRecursive(node, keys, values, middle + 1, end);\n    return node;\n  }\n  return null;\n}\n\n\nfunction sort(keys, values, left, right, compare) {\n  if (left >= right) return;\n\n  const pivot = keys[(left + right) >> 1];\n  let i = left - 1;\n  let j = right + 1;\n\n  while (true) {\n    do i++; while (compare(keys[i], pivot) < 0);\n    do j--; while (compare(keys[j], pivot) > 0);\n    if (i >= j) break;\n\n    let tmp = keys[i];\n    keys[i] = keys[j];\n    keys[j] = tmp;\n\n    tmp = values[i];\n    values[i] = values[j];\n    values[j] = tmp;\n  }\n\n  sort(keys, values,  left,     j, compare);\n  sort(keys, values, j + 1, right, compare);\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA,SAAS,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;;AAEtE,AAAe,MAAM,SAAS,CAAC;;EAE7B,WAAW,CAAC,OAAO,GAAG,eAAe,EAAE,YAAY,GAAG,KAAK,EAAE;IAC3D,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,YAAY,CAAC;GACrC;;;EAGD,UAAU,CAAC,CAAC,EAAE;IACZ,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;IAChB,IAAI,CAAC,EAAE;MACL,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC;MACjB,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;MAC9B,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;KACrB;;IAED,IAAI,CAAC,CAAC,CAAC,MAAM,iBAAiB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SACxC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;kCAClB,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;IACjD,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;IAClB,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;GACd;;;EAGD,WAAW,CAAC,CAAC,EAAE;IACb,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;IACf,IAAI,CAAC,EAAE;MACL,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC;MACjB,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;MAChC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;KACrB;;IAED,IAAI,CAAC,CAAC,CAAC,MAAM,gBAAgB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SACvC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;iCAClB,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;IAChD,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;IACnB,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;GACd;;;EAGD,MAAM,CAAC,CAAC,EAAE;IACR,OAAO,CAAC,CAAC,MAAM,EAAE;MACf,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;MACjB,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE;QACb,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;0BACpB,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;OACtC,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,EAAE;QAC9C,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAC3B,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;OACrB,MAAM,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,EAAE;QAChD,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAC1B,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;OACpB,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,EAAE;QAC/C,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;OACpB,MAAM;QACL,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;OACrB;KACF;GACF;;;EAGD,KAAK,CAAC,CAAC,EAAE;IACP,IAAI,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;;IAErB,OAAO,CAAC,CAAC,MAAM,EAAE;MACf,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;MACb,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC;;MAEd,IAAI,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE;QACnB,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC;QAChB,IAAI,GAAG,CAAC,IAAI,KAAK,EAAE,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC;6BACd,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;QACnC,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC;OAChB,MAAM;QACL,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;QAChB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;OAChB;;MAED,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;;MAExB,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE;QAChB,IAAI,EAAE,EAAE;UACN,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE;;YAEjB,IAAI,CAAC,CAAC,KAAK,EAAE;cACX,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC;cAClB,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;aACrB,MAAM,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;;YAEtB,CAAC,CAAC,KAAK,KAAK,EAAE,CAAC;YACf,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;WACf,MAAM;;YAEL,IAAI,CAAC,EAAE;cACL,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC;cACb,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC;aACf,MAAM,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC;;YAEvB,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC;YACf,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;WACf;SACF;QACD,IAAI,CAAC,EAAE;UACL,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;UACX,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;SACd,MAAM,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;;QAErB,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;QACb,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;OACd,MAAM;QACL,IAAI,EAAE,EAAE;UACN,IAAI,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE;;YAElB,IAAI,CAAC,CAAC,IAAI,EAAE;cACV,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC;cAClB,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;aACtB,MAAM,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC;;YAEvB,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;YACZ,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;WACf,MAAM;;YAEL,IAAI,CAAC,EAAE;cACL,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC;cACZ,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC;aACf,MAAM,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;;YAEtB,CAAC,CAAC,KAAK,KAAK,EAAE,CAAC;YACf,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;WACf;SACF;QACD,IAAI,CAAC,EAAE;UACL,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;UACZ,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;SACd,MAAM,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC;;QAEtB,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC;QACb,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;OACd;KACF;GACF;;;EAGD,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE;IACZ,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SACzB,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;SAC3C,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;IACxB,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;GAC5B;;;EAGD,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;IACtB,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;IACjC,OAAO,CAAC,CAAC;GACV;;;EAGD,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE;IACtB,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;IACnC,OAAO,CAAC,CAAC;GACV;;;EAGD,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE;IAChB,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;IACnB,IAAI,CAAC,GAAG,IAAI,CAAC;IACb,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;IACzB,IAAI,GAAG,CAAC;;IAER,IAAI,IAAI,CAAC,aAAa,EAAE;MACtB,OAAO,CAAC,EAAE;QACR,CAAC,GAAG,CAAC,CAAC;QACN,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACvB,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO;aACjB,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;aACtC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;OACjB;KACF,MAAM;MACL,OAAO,CAAC,EAAE;QACR,CAAC,GAAG,CAAC,CAAC;QACN,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;aACjC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;OACjB;KACF;;IAED,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;;IAEtD,IAAI,CAAC,CAAC,2BAA2B,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SAC3C,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;qCACZ,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;;IAE7C,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACd,IAAI,CAAC,KAAK,EAAE,CAAC;IACb,OAAO,CAAC,CAAC;GACV;;;EAGD,IAAI,CAAC,CAAC,GAAG,EAAE;IACT,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC;IACtB,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;IACzB,OAAO,CAAC,EAAE;MACR,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MAC3B,SAAS,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;WACzB,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBACX,OAAO,CAAC,CAAC;KAC5B;IACD,OAAO,IAAI,CAAC;GACb;;;;;;;EAOD,QAAQ,CAAC,CAAC,GAAG,EAAE;IACb,IAAI,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC;IAC5B,IAAI,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC/B,OAAO,IAAI,GAAG;MACZ,IAAI,GAAG,GAAG,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;MACpC,SAAS,GAAG,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC;WAC3B,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;0BACjB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;KACvC;;IAED,OAAO,KAAK,CAAC;GACd;;;EAGD,MAAM,CAAC,CAAC,GAAG,EAAE;IACX,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;IAEvB,IAAI,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC;;IAErB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAEd,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;SACjC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;SACtC;MACH,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;MAC9B,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;QAClB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;QAClB,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;OACpB;MACD,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;KACnB;;IAED,IAAI,CAAC,KAAK,EAAE,CAAC;IACb,OAAO,IAAI,CAAC;GACb;;;EAGD,UAAU,CAAC,CAAC,EAAE;IACZ,IAAI,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC;;IAErB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAEd,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;SACjC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;SACtC;MACH,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;MAC9B,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;QAClB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;QAClB,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;OACpB;MACD,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;KACnB;;IAED,IAAI,CAAC,KAAK,EAAE,CAAC;IACb,OAAO,IAAI,CAAC;GACb;;;EAGD,KAAK,CAAC,CAAC,GAAG,EAAE;IACV,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvB,IAAI,CAAC,CAAC,EAAE,OAAO;;IAEf,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAEd,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;IACf,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;;IAEhB,IAAI,IAAI,GAAG,IAAI,CAAC;IAChB,IAAI,CAAC,EAAE;MACL,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;MAChB,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;MACvB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;MACjB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;KACnB;IACD,IAAI,CAAC,EAAE;MACL,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;aACf,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;MACtB,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;KACjB;;IAED,IAAI,CAAC,KAAK,EAAE,CAAC;GACd;;;;;;EAMD,GAAG,CAAC,GAAG;IACL,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,WAAW,GAAG,IAAI,CAAC;IAC1C,IAAI,IAAI,EAAE;MACR,OAAO,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;MACnC,WAAW,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;MACjD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACvB;IACD,OAAO,WAAW,CAAC;GACpB;;;;;;;;;;EAUD,IAAI,CAAC,CAAC,IAAI,EAAE;IACV,IAAI,SAAS,GAAG,IAAI,CAAC;IACrB,IAAI,SAAS,EAAE;MACb,IAAI,SAAS,CAAC,KAAK,EAAE;QACnB,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC;QAC5B,OAAO,SAAS,IAAI,SAAS,CAAC,IAAI,EAAE,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC;OAChE,MAAM;QACL,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;QACxB,OAAO,SAAS,IAAI,SAAS,CAAC,KAAK,KAAK,IAAI,EAAE;UAC5C,IAAI,GAAG,SAAS,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC;SAChD;OACF;KACF;IACD,OAAO,SAAS,CAAC;GAClB;;;;;;;;EAQD,IAAI,CAAC,CAAC,IAAI,EAAE;IACV,IAAI,WAAW,GAAG,IAAI,CAAC;IACvB,IAAI,WAAW,EAAE;MACf,IAAI,WAAW,CAAC,IAAI,EAAE;QACpB,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC;QAC/B,OAAO,WAAW,IAAI,WAAW,CAAC,KAAK,EAAE,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC;OAC1E,MAAM;QACL,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,OAAO,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,EAAE;UAC/C,IAAI,GAAG,WAAW,CAAC;UACnB,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;SAClC;OACF;KACF;IACD,OAAO,WAAW,CAAC;GACpB;;;;;;;;EAQD,OAAO,CAAC,QAAQ,EAAE;IAChB,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;IACzB,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;IAEhC,OAAO,CAAC,IAAI,EAAE;;MAEZ,IAAI,OAAO,EAAE;;;QAGX,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChB,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;OACxB,MAAM;;;;QAIL,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;UAChB,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;UAClB,QAAQ,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;;;;UAIvB,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;SACzB,MAAM,IAAI,GAAG,IAAI,CAAC;OACpB;KACF;IACD,OAAO,IAAI,CAAC;GACb;;;;;;;;;;;EAWD,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE;IACxB,MAAM,CAAC,GAAG,EAAE,CAAC;IACb,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;IAC9B,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC;;IAE3B,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,EAAE;MAC7B,IAAI,IAAI,EAAE;QACR,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACb,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;OAClB,MAAM;QACL,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QACf,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAC9B,IAAI,GAAG,GAAG,CAAC,EAAE;UACX,MAAM;SACP,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;UACtC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC;SACrC;QACD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;OACnB;KACF;IACD,OAAO,IAAI,CAAC;GACb;;;;;;EAMD,IAAI,CAAC,GAAG;IACN,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;IACzB,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,GAAG,KAAK,CAAC;;IAEjC,OAAO,CAAC,IAAI,EAAE;MACZ,IAAI,OAAO,EAAE;QACX,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChB,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;OACxB,MAAM;QACL,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;UAChB,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;UAClB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;UACpB,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;SACzB,MAAM,IAAI,GAAG,IAAI,CAAC;OACpB;KACF;IACD,OAAO,CAAC,CAAC;GACV;;;;;;;EAOD,MAAM,CAAC,GAAG;IACR,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;IACzB,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,GAAG,KAAK,CAAC;;IAEjC,OAAO,CAAC,IAAI,EAAE;MACZ,IAAI,OAAO,EAAE;QACX,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChB,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;OACxB,MAAM;QACL,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;UAChB,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;UAClB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;UACrB,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;SACzB,MAAM,IAAI,GAAG,IAAI,CAAC;OACpB;KACF;IACD,OAAO,CAAC,CAAC;GACV;;;;;;;;EAQD,EAAE,CAAC,CAAC,KAAK,EAAE;;;;;IAKT,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;IACzB,IAAI,CAAC,GAAG,EAAE,EAAE,IAAI,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;IAEhC,OAAO,CAAC,IAAI,EAAE;MACZ,IAAI,OAAO,EAAE;QACX,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChB,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;OACxB,MAAM;QACL,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;UAChB,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;UAClB,IAAI,CAAC,KAAK,KAAK,EAAE,OAAO,OAAO,CAAC;UAChC,CAAC,EAAE,CAAC;UACJ,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;SACzB,MAAM,IAAI,GAAG,IAAI,CAAC;OACpB;KACF;IACD,OAAO,IAAI,CAAC;GACb;;;;;;;;;;;EAWD,IAAI,CAAC,IAAI,GAAG,EAAE,EAAE,MAAM,GAAG,EAAE,EAAE,OAAO,GAAG,KAAK,EAAE;IAC5C,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACtE,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;IACzB,IAAI,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5D,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IACxD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,OAAO,IAAI,CAAC;GACb;;;EAGD,GAAG,GAAG;IACJ,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpC,IAAI,IAAI,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC;cAChB,OAAO,IAAI,CAAC;GACvB;;;EAGD,GAAG,GAAG;IACJ,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpC,IAAI,IAAI,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC;cAChB,OAAO,IAAI,CAAC;GACvB;;EAED,OAAO,GAAG,EAAE,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,EAAE;EACzC,IAAI,IAAI,GAAG,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;;;;;;;;;;;;;;;EAejC,OAAO,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE;IACjE,OAAO,IAAI,SAAS,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;GAC5E;CACF;;;AAGD,SAAS,aAAa,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE;EACxD,MAAM,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC;EACzB,IAAI,IAAI,GAAG,CAAC,EAAE;IACZ,MAAM,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;IAC5C,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5B,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC;IAC9B,MAAM,IAAI,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IACrC,IAAI,CAAC,IAAI,MAAM,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAChE,IAAI,CAAC,KAAK,KAAK,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;IAClE,OAAO,IAAI,CAAC;GACb;EACD,OAAO,IAAI,CAAC;CACb;;;AAGD,SAAS,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE;EAChD,IAAI,IAAI,IAAI,KAAK,EAAE,OAAO;;EAE1B,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC;EACxC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;EACjB,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;;EAElB,OAAO,IAAI,EAAE;IACX,GAAG,CAAC,EAAE,CAAC,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE;IAC5C,GAAG,CAAC,EAAE,CAAC,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE;IAC5C,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM;;IAElB,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;;IAEd,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAChB,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACtB,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;GACjB;;EAED,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;EAC1C,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;CAC3C;;;;"}