{"version": 3, "file": "splay.js", "sources": ["../index.js"], "sourcesContent": ["function DEFAULT_COMPARE (a, b) { return a > b ? 1 : a < b ? -1 : 0; }\n\nexport default class SplayTree {\n\n  constructor(compare = DEFAULT_COMPARE, noDuplicates = false) {\n    this._compare = compare;\n    this._root = null;\n    this._size = 0;\n    this._noDuplicates = !!noDuplicates;\n  }\n\n\n  rotateLeft(x) {\n    var y = x.right;\n    if (y) {\n      x.right = y.left;\n      if (y.left) y.left.parent = x;\n      y.parent = x.parent;\n    }\n\n    if (!x.parent)                this._root = y;\n    else if (x === x.parent.left) x.parent.left = y;\n    else                          x.parent.right = y;\n    if (y) y.left = x;\n    x.parent = y;\n  }\n\n\n  rotateRight(x) {\n    var y = x.left;\n    if (y) {\n      x.left = y.right;\n      if (y.right) y.right.parent = x;\n      y.parent = x.parent;\n    }\n\n    if (!x.parent)               this._root = y;\n    else if(x === x.parent.left) x.parent.left = y;\n    else                         x.parent.right = y;\n    if (y) y.right = x;\n    x.parent = y;\n  }\n\n\n  _splay(x) {\n    while (x.parent) {\n      var p = x.parent;\n      if (!p.parent) {\n        if (p.left === x) this.rotateRight(p);\n        else              this.rotateLeft(p);\n      } else if (p.left === x && p.parent.left === p) {\n        this.rotateRight(p.parent);\n        this.rotateRight(p);\n      } else if (p.right === x && p.parent.right === p) {\n        this.rotateLeft(p.parent);\n        this.rotateLeft(p);\n      } else if (p.left === x && p.parent.right === p) {\n        this.rotateRight(p);\n        this.rotateLeft(p);\n      } else {\n        this.rotateLeft(p);\n        this.rotateRight(p);\n      }\n    }\n  }\n\n\n  splay(x) {\n    var p, gp, ggp, l, r;\n\n    while (x.parent) {\n      p = x.parent;\n      gp = p.parent;\n\n      if (gp && gp.parent) {\n        ggp = gp.parent;\n        if (ggp.left === gp) ggp.left  = x;\n        else                 ggp.right = x;\n        x.parent = ggp;\n      } else {\n        x.parent = null;\n        this._root = x;\n      }\n\n      l = x.left; r = x.right;\n\n      if (x === p.left) { // left\n        if (gp) {\n          if (gp.left === p) {\n            /* zig-zig */\n            if (p.right) {\n              gp.left = p.right;\n              gp.left.parent = gp;\n            } else gp.left = null;\n\n            p.right   = gp;\n            gp.parent = p;\n          } else {\n            /* zig-zag */\n            if (l) {\n              gp.right = l;\n              l.parent = gp;\n            } else gp.right = null;\n\n            x.left    = gp;\n            gp.parent = x;\n          }\n        }\n        if (r) {\n          p.left = r;\n          r.parent = p;\n        } else p.left = null;\n\n        x.right  = p;\n        p.parent = x;\n      } else { // right\n        if (gp) {\n          if (gp.right === p) {\n            /* zig-zig */\n            if (p.left) {\n              gp.right = p.left;\n              gp.right.parent = gp;\n            } else gp.right = null;\n\n            p.left = gp;\n            gp.parent = p;\n          } else {\n            /* zig-zag */\n            if (r) {\n              gp.left = r;\n              r.parent = gp;\n            } else gp.left = null;\n\n            x.right   = gp;\n            gp.parent = x;\n          }\n        }\n        if (l) {\n          p.right = l;\n          l.parent = p;\n        } else p.right = null;\n\n        x.left   = p;\n        p.parent = x;\n      }\n    }\n  }\n\n\n  replace(u, v) {\n    if (!u.parent) this._root = v;\n    else if (u === u.parent.left) u.parent.left = v;\n    else u.parent.right = v;\n    if (v) v.parent = u.parent;\n  }\n\n\n  minNode(u = this._root) {\n    if (u) while (u.left) u = u.left;\n    return u;\n  }\n\n\n  maxNode(u = this._root) {\n    if (u) while (u.right) u = u.right;\n    return u;\n  }\n\n\n  insert(key, data) {\n    var z = this._root;\n    var p = null;\n    var comp = this._compare;\n    var cmp;\n\n    if (this._noDuplicates) {\n      while (z) {\n        p = z;\n        cmp = comp(z.key, key);\n        if (cmp === 0) return;\n        else if (comp(z.key, key) < 0) z = z.right;\n        else z = z.left;\n      }\n    } else {\n      while (z) {\n        p = z;\n        if (comp(z.key, key) < 0) z = z.right;\n        else z = z.left;\n      }\n    }\n\n    z = { key, data, left: null, right: null, parent: p };\n\n    if (!p)                          this._root = z;\n    else if (comp(p.key, z.key) < 0) p.right = z;\n    else                             p.left  = z;\n\n    this.splay(z);\n    this._size++;\n    return z;\n  }\n\n\n  find (key) {\n    var z    = this._root;\n    var comp = this._compare;\n    while (z) {\n      var cmp = comp(z.key, key);\n      if      (cmp < 0) z = z.right;\n      else if (cmp > 0) z = z.left;\n      else              return z;\n    }\n    return null;\n  }\n\n  /**\n   * Whether the tree contains a node with the given key\n   * @param  {Key} key\n   * @return {boolean} true/false\n   */\n  contains (key) {\n    var node       = this._root;\n    var comparator = this._compare;\n    while (node)  {\n      var cmp = comparator(key, node.key);\n      if      (cmp === 0) return true;\n      else if (cmp < 0)   node = node.left;\n      else                node = node.right;\n    }\n\n    return false;\n  }\n\n\n  remove (key) {\n    var z = this.find(key);\n\n    if (!z) return false;\n\n    this.splay(z);\n\n    if (!z.left) this.replace(z, z.right);\n    else if (!z.right) this.replace(z, z.left);\n    else {\n      var y = this.minNode(z.right);\n      if (y.parent !== z) {\n        this.replace(y, y.right);\n        y.right = z.right;\n        y.right.parent = y;\n      }\n      this.replace(z, y);\n      y.left = z.left;\n      y.left.parent = y;\n    }\n\n    this._size--;\n    return true;\n  }\n\n\n  removeNode(z) {\n    if (!z) return false;\n\n    this.splay(z);\n\n    if (!z.left) this.replace(z, z.right);\n    else if (!z.right) this.replace(z, z.left);\n    else {\n      var y = this.minNode(z.right);\n      if (y.parent !== z) {\n        this.replace(y, y.right);\n        y.right = z.right;\n        y.right.parent = y;\n      }\n      this.replace(z, y);\n      y.left = z.left;\n      y.left.parent = y;\n    }\n\n    this._size--;\n    return true;\n  }\n\n\n  erase (key) {\n    var z = this.find(key);\n    if (!z) return;\n\n    this.splay(z);\n\n    var s = z.left;\n    var t = z.right;\n\n    var sMax = null;\n    if (s) {\n      s.parent = null;\n      sMax = this.maxNode(s);\n      this.splay(sMax);\n      this._root = sMax;\n    }\n    if (t) {\n      if (s) sMax.right = t;\n      else   this._root = t;\n      t.parent = sMax;\n    }\n\n    this._size--;\n  }\n\n  /**\n   * Removes and returns the node with smallest key\n   * @return {?Node}\n   */\n  pop () {\n    var node = this._root, returnValue = null;\n    if (node) {\n      while (node.left) node = node.left;\n      returnValue = { key: node.key, data: node.data };\n      this.remove(node.key);\n    }\n    return returnValue;\n  }\n\n\n  /* eslint-disable class-methods-use-this */\n\n  /**\n   * Successor node\n   * @param  {Node} node\n   * @return {?Node}\n   */\n  next (node) {\n    var successor = node;\n    if (successor) {\n      if (successor.right) {\n        successor = successor.right;\n        while (successor && successor.left) successor = successor.left;\n      } else {\n        successor = node.parent;\n        while (successor && successor.right === node) {\n          node = successor; successor = successor.parent;\n        }\n      }\n    }\n    return successor;\n  }\n\n\n  /**\n   * Predecessor node\n   * @param  {Node} node\n   * @return {?Node}\n   */\n  prev (node) {\n    var predecessor = node;\n    if (predecessor) {\n      if (predecessor.left) {\n        predecessor = predecessor.left;\n        while (predecessor && predecessor.right) predecessor = predecessor.right;\n      } else {\n        predecessor = node.parent;\n        while (predecessor && predecessor.left === node) {\n          node = predecessor;\n          predecessor = predecessor.parent;\n        }\n      }\n    }\n    return predecessor;\n  }\n  /* eslint-enable class-methods-use-this */\n\n\n  /**\n   * @param  {forEachCallback} callback\n   * @return {SplayTree}\n   */\n  forEach(callback) {\n    var current = this._root;\n    var s = [], done = false, i = 0;\n\n    while (!done) {\n      // Reach the left most Node of the current Node\n      if (current) {\n        // Place pointer to a tree node on the stack\n        // before traversing the node's left subtree\n        s.push(current);\n        current = current.left;\n      } else {\n        // BackTrack from the empty subtree and visit the Node\n        // at the top of the stack; however, if the stack is\n        // empty you are done\n        if (s.length > 0) {\n          current = s.pop();\n          callback(current, i++);\n\n          // We have visited the node and its left\n          // subtree. Now, it's right subtree's turn\n          current = current.right;\n        } else done = true;\n      }\n    }\n    return this;\n  }\n\n\n  /**\n   * Walk key range from `low` to `high`. Stops if `fn` returns a value.\n   * @param  {Key}      low\n   * @param  {Key}      high\n   * @param  {Function} fn\n   * @param  {*?}       ctx\n   * @return {SplayTree}\n   */\n  range(low, high, fn, ctx) {\n    const Q = [];\n    const compare = this._compare;\n    let node = this._root, cmp;\n\n    while (Q.length !== 0 || node) {\n      if (node) {\n        Q.push(node);\n        node = node.left;\n      } else {\n        node = Q.pop();\n        cmp = compare(node.key, high);\n        if (cmp > 0) {\n          break;\n        } else if (compare(node.key, low) >= 0) {\n          if (fn.call(ctx, node)) return this; // stop if smth is returned\n        }\n        node = node.right;\n      }\n    }\n    return this;\n  }\n\n  /**\n   * Returns all keys in order\n   * @return {Array<Key>}\n   */\n  keys () {\n    var current = this._root;\n    var s = [], r = [], done = false;\n\n    while (!done) {\n      if (current) {\n        s.push(current);\n        current = current.left;\n      } else {\n        if (s.length > 0) {\n          current = s.pop();\n          r.push(current.key);\n          current = current.right;\n        } else done = true;\n      }\n    }\n    return r;\n  }\n\n\n  /**\n   * Returns `data` fields of all nodes in order.\n   * @return {Array<Value>}\n   */\n  values () {\n    var current = this._root;\n    var s = [], r = [], done = false;\n\n    while (!done) {\n      if (current) {\n        s.push(current);\n        current = current.left;\n      } else {\n        if (s.length > 0) {\n          current = s.pop();\n          r.push(current.data);\n          current = current.right;\n        } else done = true;\n      }\n    }\n    return r;\n  }\n\n\n  /**\n   * Returns node at given index\n   * @param  {number} index\n   * @return {?Node}\n   */\n  at (index) {\n    // removed after a consideration, more misleading than useful\n    // index = index % this.size;\n    // if (index < 0) index = this.size - index;\n\n    var current = this._root;\n    var s = [], done = false, i = 0;\n\n    while (!done) {\n      if (current) {\n        s.push(current);\n        current = current.left;\n      } else {\n        if (s.length > 0) {\n          current = s.pop();\n          if (i === index) return current;\n          i++;\n          current = current.right;\n        } else done = true;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Bulk-load items. Both array have to be same size\n   * @param  {Array<Key>}    keys\n   * @param  {Array<Value>}  [values]\n   * @param  {Boolean}       [presort=false] Pre-sort keys and values, using\n   *                                         tree's comparator. Sorting is done\n   *                                         in-place\n   * @return {AVLTree}\n   */\n  load(keys = [], values = [], presort = false) {\n    if (this._size !== 0) throw new Error('bulk-load: tree is not empty');\n    const size = keys.length;\n    if (presort) sort(keys, values, 0, size - 1, this._compare);\n    this._root = loadRecursive(null, keys, values, 0, size);\n    this._size = size;\n    return this;\n  }\n\n\n  min() {\n    var node = this.minNode(this._root);\n    if (node) return node.key;\n    else      return null;\n  }\n\n\n  max() {\n    var node = this.maxNode(this._root);\n    if (node) return node.key;\n    else      return null;\n  }\n\n  isEmpty() { return this._root === null; }\n  get size() { return this._size; }\n\n\n  /**\n   * Create a tree and load it with items\n   * @param  {Array<Key>}          keys\n   * @param  {Array<Value>?}        [values]\n\n   * @param  {Function?}            [comparator]\n   * @param  {Boolean?}             [presort=false] Pre-sort keys and values, using\n   *                                               tree's comparator. Sorting is done\n   *                                               in-place\n   * @param  {Boolean?}             [noDuplicates=false]   Allow duplicates\n   * @return {SplayTree}\n   */\n  static createTree(keys, values, comparator, presort, noDuplicates) {\n    return new SplayTree(comparator, noDuplicates).load(keys, values, presort);\n  }\n}\n\n\nfunction loadRecursive (parent, keys, values, start, end) {\n  const size = end - start;\n  if (size > 0) {\n    const middle = start + Math.floor(size / 2);\n    const key    = keys[middle];\n    const data   = values[middle];\n    const node   = { key, data, parent };\n    node.left    = loadRecursive(node, keys, values, start, middle);\n    node.right   = loadRecursive(node, keys, values, middle + 1, end);\n    return node;\n  }\n  return null;\n}\n\n\nfunction sort(keys, values, left, right, compare) {\n  if (left >= right) return;\n\n  const pivot = keys[(left + right) >> 1];\n  let i = left - 1;\n  let j = right + 1;\n\n  while (true) {\n    do i++; while (compare(keys[i], pivot) < 0);\n    do j--; while (compare(keys[j], pivot) > 0);\n    if (i >= j) break;\n\n    let tmp = keys[i];\n    keys[i] = keys[j];\n    keys[j] = tmp;\n\n    tmp = values[i];\n    values[i] = values[j];\n    values[j] = tmp;\n  }\n\n  sort(keys, values,  left,     j, compare);\n  sort(keys, values, j + 1, right, compare);\n}\n"], "names": ["this", "const", "let"], "mappings": ";;;;;;;;;;;;;;;AAAA,SAAS,eAAe,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;;AAEtE,IAAqB,SAAS,GAAC,kBAElB,CAAC,OAAyB,EAAE,YAAoB,EAAE;mCAA1C,GAAG,eAAe,CAAc;6CAAA,GAAG,KAAK;;EAC3D,IAAM,CAAC,QAAQ,GAAG,OAAO,CAAC;EAC1B,IAAM,CAAC,KAAK,GAAG,IAAI,CAAC;EACpB,IAAM,CAAC,KAAK,GAAG,CAAC,CAAC;EACjB,IAAM,CAAC,aAAa,GAAG,CAAC,CAAC,YAAY,CAAC;CACrC;;sCAAA;;;AAGH,oBAAE,UAAU,wBAAC,CAAC,EAAE;EACd,IAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;EAClB,IAAM,CAAC,EAAE;IACP,CAAG,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC;IACnB,IAAM,CAAC,CAAC,IAAI,EAAE,EAAA,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAA;IAChC,CAAG,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;GACrB;;EAEH,IAAM,CAAC,CAAC,CAAC,MAAM,eAAe,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;OACxC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,EAAA,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,EAAA;8BACpB,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;EACnD,IAAM,CAAC,EAAE,EAAA,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,EAAA;EACpB,CAAG,CAAC,MAAM,GAAG,CAAC,CAAC;CACd,CAAA;;;AAGH,oBAAE,WAAW,yBAAC,CAAC,EAAE;EACf,IAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;EACjB,IAAM,CAAC,EAAE;IACP,CAAG,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC;IACnB,IAAM,CAAC,CAAC,KAAK,EAAE,EAAA,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAA;IAClC,CAAG,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;GACrB;;EAEH,IAAM,CAAC,CAAC,CAAC,MAAM,cAAc,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;OACvC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,EAAA,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,EAAA;6BACpB,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;EAClD,IAAM,CAAC,EAAE,EAAA,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;EACrB,CAAG,CAAC,MAAM,GAAG,CAAC,CAAC;CACd,CAAA;;;AAGH,oBAAE,MAAM,oBAAC,CAAC,EAAE;;;EACV,OAAS,CAAC,CAAC,MAAM,EAAE;IACjB,IAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;IACnB,IAAM,CAAC,CAAC,CAAC,MAAM,EAAE;MACf,IAAM,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE,EAAAA,MAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAA;sBACtB,EAAEA,MAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAA;KACtC,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,EAAE;MAChD,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;MAC7B,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;KACrB,MAAM,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,EAAE;MAClD,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;MAC5B,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;KACpB,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,EAAE;MACjD,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;MACtB,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;KACpB,MAAM;MACP,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;MACrB,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;KACrB;GACF;CACF,CAAA;;;AAGH,oBAAE,KAAK,mBAAC,CAAC,EAAE;;;EACT,IAAM,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;;EAEvB,OAAS,CAAC,CAAC,MAAM,EAAE;IACjB,CAAG,GAAG,CAAC,CAAC,MAAM,CAAC;IACf,EAAI,GAAG,CAAC,CAAC,MAAM,CAAC;;IAEhB,IAAM,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE;MACrB,GAAK,GAAG,EAAE,CAAC,MAAM,CAAC;MAClB,IAAM,GAAG,CAAC,IAAI,KAAK,EAAE,EAAE,EAAA,GAAG,CAAC,IAAI,EAAI,CAAC,CAAC,EAAA;yBAChB,EAAE,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;MACrC,CAAG,CAAC,MAAM,GAAG,GAAG,CAAC;KAChB,MAAM;MACP,CAAG,CAAC,MAAM,GAAG,IAAI,CAAC;MAClB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;KAChB;;IAEH,CAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;;IAE1B,IAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE;MAClB,IAAM,EAAE,EAAE;QACR,IAAM,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE;;UAEnB,IAAM,CAAC,CAAC,KAAK,EAAE;YACb,EAAI,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC;YACpB,EAAI,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;WACrB,MAAM,EAAA,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,EAAA;;UAExB,CAAG,CAAC,KAAK,GAAK,EAAE,CAAC;UACjB,EAAI,CAAC,MAAM,GAAG,CAAC,CAAC;SACf,MAAM;;UAEP,IAAM,CAAC,EAAE;YACP,EAAI,CAAC,KAAK,GAAG,CAAC,CAAC;YACf,CAAG,CAAC,MAAM,GAAG,EAAE,CAAC;WACf,MAAM,EAAA,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,EAAA;;UAEzB,CAAG,CAAC,IAAI,IAAM,EAAE,CAAC;UACjB,EAAI,CAAC,MAAM,GAAG,CAAC,CAAC;SACf;OACF;MACH,IAAM,CAAC,EAAE;QACP,CAAG,CAAC,IAAI,GAAG,CAAC,CAAC;QACb,CAAG,CAAC,MAAM,GAAG,CAAC,CAAC;OACd,MAAM,EAAA,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,EAAA;;MAEvB,CAAG,CAAC,KAAK,EAAI,CAAC,CAAC;MACf,CAAG,CAAC,MAAM,GAAG,CAAC,CAAC;KACd,MAAM;MACP,IAAM,EAAE,EAAE;QACR,IAAM,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE;;UAEpB,IAAM,CAAC,CAAC,IAAI,EAAE;YACZ,EAAI,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC;YACpB,EAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;WACtB,MAAM,EAAA,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,EAAA;;UAEzB,CAAG,CAAC,IAAI,GAAG,EAAE,CAAC;UACd,EAAI,CAAC,MAAM,GAAG,CAAC,CAAC;SACf,MAAM;;UAEP,IAAM,CAAC,EAAE;YACP,EAAI,CAAC,IAAI,GAAG,CAAC,CAAC;YACd,CAAG,CAAC,MAAM,GAAG,EAAE,CAAC;WACf,MAAM,EAAA,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,EAAA;;UAExB,CAAG,CAAC,KAAK,GAAK,EAAE,CAAC;UACjB,EAAI,CAAC,MAAM,GAAG,CAAC,CAAC;SACf;OACF;MACH,IAAM,CAAC,EAAE;QACP,CAAG,CAAC,KAAK,GAAG,CAAC,CAAC;QACd,CAAG,CAAC,MAAM,GAAG,CAAC,CAAC;OACd,MAAM,EAAA,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,EAAA;;MAExB,CAAG,CAAC,IAAI,GAAK,CAAC,CAAC;MACf,CAAG,CAAC,MAAM,GAAG,CAAC,CAAC;KACd;GACF;CACF,CAAA;;;AAGH,oBAAE,OAAO,qBAAC,CAAC,EAAE,CAAC,EAAE;EACd,IAAM,CAAC,CAAC,CAAC,MAAM,EAAE,EAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;OACzB,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,EAAA,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,EAAA;OAC3C,EAAA,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;EAC1B,IAAM,CAAC,EAAE,EAAA,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,EAAA;CAC5B,CAAA;;;AAGH,oBAAE,OAAO,qBAAC,CAAc,EAAE;yBAAf,GAAG,IAAI,CAAC,KAAK;;EACtB,IAAM,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,IAAI,EAAE,EAAA,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAA;EACnC,OAAS,CAAC,CAAC;CACV,CAAA;;;AAGH,oBAAE,OAAO,qBAAC,CAAc,EAAE;yBAAf,GAAG,IAAI,CAAC,KAAK;;EACtB,IAAM,CAAC,EAAE,EAAA,OAAO,CAAC,CAAC,KAAK,EAAE,EAAA,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAA;EACrC,OAAS,CAAC,CAAC;CACV,CAAA;;;AAGH,oBAAE,MAAM,oBAAC,GAAG,EAAE,IAAI,EAAE;EAClB,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;EACrB,IAAM,CAAC,GAAG,IAAI,CAAC;EACf,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;EAC3B,IAAM,GAAG,CAAC;;EAEV,IAAM,IAAI,CAAC,aAAa,EAAE;IACxB,OAAS,CAAC,EAAE;MACV,CAAG,GAAG,CAAC,CAAC;MACR,GAAK,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MACzB,IAAM,GAAG,KAAK,CAAC,EAAE,EAAA,OAAO,EAAA;WACjB,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,EAAA,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAA;WACtC,EAAA,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAA;KACjB;GACF,MAAM;IACP,OAAS,CAAC,EAAE;MACV,CAAG,GAAG,CAAC,CAAC;MACR,IAAM,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,EAAA,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAA;WACjC,EAAA,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAA;KACjB;GACF;;EAEH,CAAG,GAAG,EAAE,KAAA,GAAG,EAAE,MAAA,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;;EAExD,IAAM,CAAC,CAAC,yBAAyB,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;OAC3C,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EAAA,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;iCACd,EAAE,CAAC,CAAC,IAAI,EAAI,CAAC,CAAC,EAAA;;EAE/C,IAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAChB,IAAM,CAAC,KAAK,EAAE,CAAC;EACf,OAAS,CAAC,CAAC;CACV,CAAA;;;AAGH,oBAAE,IAAI,kBAAE,GAAG,EAAE;EACX,IAAM,CAAC,IAAM,IAAI,CAAC,KAAK,CAAC;EACxB,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;EAC3B,OAAS,CAAC,EAAE;IACV,IAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IAC7B,OAAW,GAAG,GAAG,CAAC,EAAE,EAAA,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAA;SACzB,IAAI,GAAG,GAAG,CAAC,EAAE,EAAA,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAA;oBACb,EAAE,OAAO,CAAC,CAAC,EAAA;GAC5B;EACH,OAAS,IAAI,CAAC;CACb,CAAA;;;;;;;AAOH,oBAAE,QAAQ,sBAAE,GAAG,EAAE;EACf,IAAM,IAAI,OAAS,IAAI,CAAC,KAAK,CAAC;EAC9B,IAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC;EACjC,OAAS,IAAI,CAAC;IACZ,IAAM,GAAG,GAAG,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IACtC,OAAW,GAAG,KAAK,CAAC,EAAE,EAAA,OAAO,IAAI,CAAC,EAAA;SAC3B,IAAI,GAAG,GAAG,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,EAAA;sBACnB,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,EAAA;GACvC;;EAEH,OAAS,KAAK,CAAC;CACd,CAAA;;;AAGH,oBAAE,MAAM,oBAAE,GAAG,EAAE;EACb,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;EAEzB,IAAM,CAAC,CAAC,EAAE,EAAA,OAAO,KAAK,CAAC,EAAA;;EAEvB,IAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEhB,IAAM,CAAC,CAAC,CAAC,IAAI,EAAE,EAAA,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;OACjC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,EAAA,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAA;OACtC;IACL,IAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAChC,IAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;MACpB,IAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;MAC3B,CAAG,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;MACpB,CAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;KACpB;IACH,IAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrB,CAAG,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;IAClB,CAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;GACnB;;EAEH,IAAM,CAAC,KAAK,EAAE,CAAC;EACf,OAAS,IAAI,CAAC;CACb,CAAA;;;AAGH,oBAAE,UAAU,wBAAC,CAAC,EAAE;EACd,IAAM,CAAC,CAAC,EAAE,EAAA,OAAO,KAAK,CAAC,EAAA;;EAEvB,IAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEhB,IAAM,CAAC,CAAC,CAAC,IAAI,EAAE,EAAA,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAA;OACjC,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,EAAA,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAA;OACtC;IACL,IAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAChC,IAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;MACpB,IAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;MAC3B,CAAG,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;MACpB,CAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;KACpB;IACH,IAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrB,CAAG,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;IAClB,CAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;GACnB;;EAEH,IAAM,CAAC,KAAK,EAAE,CAAC;EACf,OAAS,IAAI,CAAC;CACb,CAAA;;;AAGH,oBAAE,KAAK,mBAAE,GAAG,EAAE;EACZ,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EACzB,IAAM,CAAC,CAAC,EAAE,EAAA,OAAO,EAAA;;EAEjB,IAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEhB,IAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;EACjB,IAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;;EAElB,IAAM,IAAI,GAAG,IAAI,CAAC;EAClB,IAAM,CAAC,EAAE;IACP,CAAG,CAAC,MAAM,GAAG,IAAI,CAAC;IAClB,IAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACzB,IAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACnB,IAAM,CAAC,KAAK,GAAG,IAAI,CAAC;GACnB;EACH,IAAM,CAAC,EAAE;IACP,IAAM,CAAC,EAAE,EAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;SACjB,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAA;IACxB,CAAG,CAAC,MAAM,GAAG,IAAI,CAAC;GACjB;;EAEH,IAAM,CAAC,KAAK,EAAE,CAAC;CACd,CAAA;;;;;;AAMH,oBAAE,GAAG,mBAAI;EACP,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,WAAW,GAAG,IAAI,CAAC;EAC5C,IAAM,IAAI,EAAE;IACV,OAAS,IAAI,CAAC,IAAI,EAAE,EAAA,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,EAAA;IACrC,WAAa,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;IACnD,IAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;GACvB;EACH,OAAS,WAAW,CAAC;CACpB,CAAA;;;;;;;;;;AAUH,oBAAE,IAAI,kBAAE,IAAI,EAAE;EACZ,IAAM,SAAS,GAAG,IAAI,CAAC;EACvB,IAAM,SAAS,EAAE;IACf,IAAM,SAAS,CAAC,KAAK,EAAE;MACrB,SAAW,GAAG,SAAS,CAAC,KAAK,CAAC;MAC9B,OAAS,SAAS,IAAI,SAAS,CAAC,IAAI,EAAE,EAAA,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,EAAA;KAChE,MAAM;MACP,SAAW,GAAG,IAAI,CAAC,MAAM,CAAC;MAC1B,OAAS,SAAS,IAAI,SAAS,CAAC,KAAK,KAAK,IAAI,EAAE;QAC9C,IAAM,GAAG,SAAS,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC;OAChD;KACF;GACF;EACH,OAAS,SAAS,CAAC;CAClB,CAAA;;;;;;;;AAQH,oBAAE,IAAI,kBAAE,IAAI,EAAE;EACZ,IAAM,WAAW,GAAG,IAAI,CAAC;EACzB,IAAM,WAAW,EAAE;IACjB,IAAM,WAAW,CAAC,IAAI,EAAE;MACtB,WAAa,GAAG,WAAW,CAAC,IAAI,CAAC;MACjC,OAAS,WAAW,IAAI,WAAW,CAAC,KAAK,EAAE,EAAA,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,EAAA;KAC1E,MAAM;MACP,WAAa,GAAG,IAAI,CAAC,MAAM,CAAC;MAC5B,OAAS,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,EAAE;QACjD,IAAM,GAAG,WAAW,CAAC;QACrB,WAAa,GAAG,WAAW,CAAC,MAAM,CAAC;OAClC;KACF;GACF;EACH,OAAS,WAAW,CAAC;CACpB,CAAA;;;;;;;;AAQH,oBAAE,OAAO,qBAAC,QAAQ,EAAE;EAClB,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;EAC3B,IAAM,CAAC,GAAG,EAAE,EAAE,IAAI,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;EAElC,OAAS,CAAC,IAAI,EAAE;;IAEd,IAAM,OAAO,EAAE;;;MAGb,CAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;MAClB,OAAS,GAAG,OAAO,CAAC,IAAI,CAAC;KACxB,MAAM;;;;MAIP,IAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;QAClB,OAAS,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QACpB,QAAU,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;;;;QAIzB,OAAS,GAAG,OAAO,CAAC,KAAK,CAAC;OACzB,MAAM,EAAA,IAAI,GAAG,IAAI,CAAC,EAAA;KACpB;GACF;EACH,OAAS,IAAI,CAAC;CACb,CAAA;;;;;;;;;;;AAWH,oBAAE,KAAK,mBAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE;;;EAC1B,IAAQ,CAAC,GAAG,EAAE,CAAC;EACf,IAAQ,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;EAChC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC;;EAE7B,OAAS,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,EAAE;IAC/B,IAAM,IAAI,EAAE;MACV,CAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;MACf,IAAM,GAAG,IAAI,CAAC,IAAI,CAAC;KAClB,MAAM;MACP,IAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;MACjB,GAAK,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;MAChC,IAAM,GAAG,GAAG,CAAC,EAAE;QACb,MAAQ;OACP,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;QACxC,IAAM,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,EAAA,OAAOA,MAAI,CAAC,EAAA;OACrC;MACH,IAAM,GAAG,IAAI,CAAC,KAAK,CAAC;KACnB;GACF;EACH,OAAS,IAAI,CAAC;CACb,CAAA;;;;;;AAMH,oBAAE,IAAI,oBAAI;EACR,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;EAC3B,IAAM,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,GAAG,KAAK,CAAC;;EAEnC,OAAS,CAAC,IAAI,EAAE;IACd,IAAM,OAAO,EAAE;MACb,CAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;MAClB,OAAS,GAAG,OAAO,CAAC,IAAI,CAAC;KACxB,MAAM;MACP,IAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;QAClB,OAAS,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QACpB,CAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACtB,OAAS,GAAG,OAAO,CAAC,KAAK,CAAC;OACzB,MAAM,EAAA,IAAI,GAAG,IAAI,CAAC,EAAA;KACpB;GACF;EACH,OAAS,CAAC,CAAC;CACV,CAAA;;;;;;;AAOH,oBAAE,MAAM,sBAAI;EACV,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;EAC3B,IAAM,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,GAAG,KAAK,CAAC;;EAEnC,OAAS,CAAC,IAAI,EAAE;IACd,IAAM,OAAO,EAAE;MACb,CAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;MAClB,OAAS,GAAG,OAAO,CAAC,IAAI,CAAC;KACxB,MAAM;MACP,IAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;QAClB,OAAS,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QACpB,CAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACvB,OAAS,GAAG,OAAO,CAAC,KAAK,CAAC;OACzB,MAAM,EAAA,IAAI,GAAG,IAAI,CAAC,EAAA;KACpB;GACF;EACH,OAAS,CAAC,CAAC;CACV,CAAA;;;;;;;;AAQH,oBAAE,EAAE,gBAAE,KAAK,EAAE;;;;;EAKX,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;EAC3B,IAAM,CAAC,GAAG,EAAE,EAAE,IAAI,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;EAElC,OAAS,CAAC,IAAI,EAAE;IACd,IAAM,OAAO,EAAE;MACb,CAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;MAClB,OAAS,GAAG,OAAO,CAAC,IAAI,CAAC;KACxB,MAAM;MACP,IAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;QAClB,OAAS,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QACpB,IAAM,CAAC,KAAK,KAAK,EAAE,EAAA,OAAO,OAAO,CAAC,EAAA;QAClC,CAAG,EAAE,CAAC;QACN,OAAS,GAAG,OAAO,CAAC,KAAK,CAAC;OACzB,MAAM,EAAA,IAAI,GAAG,IAAI,CAAC,EAAA;KACpB;GACF;EACH,OAAS,IAAI,CAAC;CACb,CAAA;;;;;;;;;;;AAWH,oBAAE,IAAI,kBAAC,IAAS,EAAE,MAAW,EAAE,OAAe,EAAE;+BAArC,GAAG,EAAE,CAAQ;mCAAA,GAAG,EAAE,CAAS;qCAAA,GAAG,KAAK;;EAC5C,IAAM,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE,EAAA,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC,EAAA;EACxE,IAAQ,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;EAC3B,IAAM,OAAO,EAAE,EAAA,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAA;EAC9D,IAAM,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;EAC1D,IAAM,CAAC,KAAK,GAAG,IAAI,CAAC;EACpB,OAAS,IAAI,CAAC;CACb,CAAA;;;AAGH,oBAAE,GAAG,mBAAG;EACN,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACtC,IAAM,IAAI,EAAE,EAAA,OAAO,IAAI,CAAC,GAAG,CAAC,EAAA;UAClB,EAAE,OAAO,IAAI,CAAC,EAAA;CACvB,CAAA;;;AAGH,oBAAE,GAAG,mBAAG;EACN,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACtC,IAAM,IAAI,EAAE,EAAA,OAAO,IAAI,CAAC,GAAG,CAAC,EAAA;UAClB,EAAE,OAAO,IAAI,CAAC,EAAA;CACvB,CAAA;;AAEH,oBAAE,OAAO,uBAAG,EAAE,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,EAAE,CAAA;AAC3C,mBAAE,IAAQ,mBAAG,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;;;;;;;;;;;;;;;AAenC,UAAE,UAAiB,wBAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE;EACnE,OAAS,IAAI,SAAS,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;CAC5E,CAAA;;mEACF;;AAGD,SAAS,aAAa,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE;EACxDC,IAAM,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC;EACzB,IAAI,IAAI,GAAG,CAAC,EAAE;IACZA,IAAM,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;IAC5CA,IAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5BA,IAAM,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC;IAC9BA,IAAM,IAAI,KAAK,EAAE,KAAA,GAAG,EAAE,MAAA,IAAI,EAAE,QAAA,MAAM,EAAE,CAAC;IACrC,IAAI,CAAC,IAAI,MAAM,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAChE,IAAI,CAAC,KAAK,KAAK,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;IAClE,OAAO,IAAI,CAAC;GACb;EACD,OAAO,IAAI,CAAC;CACb;;;AAGD,SAAS,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE;EAChD,IAAI,IAAI,IAAI,KAAK,EAAE,EAAA,OAAO,EAAA;;EAE1BA,IAAM,KAAK,GAAG,IAAI,CAAC,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC;EACxCC,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;EACjBA,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;;EAElB,OAAO,IAAI,EAAE;IACX,GAAG,EAAA,CAAC,EAAE,CAAC,EAAA,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE;IAC5C,GAAG,EAAA,CAAC,EAAE,CAAC,EAAA,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE;IAC5C,IAAI,CAAC,IAAI,CAAC,EAAE,EAAA,MAAM,EAAA;;IAElBA,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;;IAEd,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAChB,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IACtB,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;GACjB;;EAED,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;EAC1C,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;CAC3C;;;;;;;;"}