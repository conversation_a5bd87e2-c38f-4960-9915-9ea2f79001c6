/**
 * API响应类型定义
 */

export interface ApiResponse {
  success: boolean
  data: {
    workout: WorkoutResponse
    animation: AnimationResponse
    rendering: RenderingResponse
    terrain: TerrainResponse
    metadata: MetadataResponse
  }
  timestamp: string
  version: string
  workout?: any // 兼容现有数据结构
}

export interface WorkoutResponse {
  id: string
  name: string
  type: string
  date: string
  duration: number
  distance: number
  elevationGain: number
  elevationLoss: number
  maxSpeed: number
  avgSpeed: number
  maxHeartRate: number
  avgHeartRate: number
  calories: number
  startLocation: {
    lat: number
    lng: number
    name: string
  }
  endLocation: {
    lat: number
    lng: number
    name: string
  }
}

export interface AnimationResponse {
  duration: number
  fps: number
  totalFrames: number
  cameraSettings: {
    followTrack: boolean
    smoothing: number
    heightOffset: number
    lookAhead: number
  }
}

export interface RenderingResponse {
  trackColor: {
    start: string
    end: string
    encoding: string
  }
  lineWidth: number
  opacity: number
  shadows: boolean
}

export interface TerrainResponse {
  enabled: boolean
  exaggeration: number
  source: string
  varianceImageUrl: string
}

export interface MetadataResponse {
  device: string
  software: string
  gpsAccuracy: string
  samplingRate: number
  dataQuality: number
}

export interface ApiError {
  code: string
  message: string
  details?: any
}

export interface ApiRequestConfig {
  timeout?: number
  retries?: number
  cache?: boolean
}
