{"name": "@mapbox/vector-tile", "description": "Parses vector tiles", "repository": {"url": "git+https://github.com/mapbox/vector-tile-js.git"}, "version": "2.0.4", "type": "module", "exports": "./index.js", "license": "BSD-3-<PERSON><PERSON>", "main": "index.js", "types": "index.d.ts", "dependencies": {"@mapbox/point-geometry": "~1.1.0", "@types/geojson": "^7946.0.16", "pbf": "^4.0.1"}, "devDependencies": {"benchmark": "^2.1.4", "eslint": "^9.29.0", "eslint-config-mourner": "^4.0.2", "typescript": "^5.8.3"}, "scripts": {"lint": "eslint index.js test/*.js", "pretest": "npm run lint", "test": "tsc && node --test", "cov": "node --test --experimental-test-coverage", "prepublishOnly": "npm test"}, "files": ["index.d.ts"]}