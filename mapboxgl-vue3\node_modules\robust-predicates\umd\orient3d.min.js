!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t=t||self).predicates={})}(this,function(t){"use strict";const e=134217729,n=33306690738754706e-32;function r(t,e,n,r,o){let f,u,s,i,a=e[0],c=r[0],l=0,b=0;c>a==c>-a?(f=a,a=e[++l]):(f=c,c=r[++b]);let d=0;if(l<t&&b<n)for(c>a==c>-a?(s=f-((u=a+f)-a),a=e[++l]):(s=f-((u=c+f)-c),c=r[++b]),f=u,0!==s&&(o[d++]=s);l<t&&b<n;)c>a==c>-a?(s=f-((u=f+a)-(i=u-f))+(a-i),a=e[++l]):(s=f-((u=f+c)-(i=u-f))+(c-i),c=r[++b]),f=u,0!==s&&(o[d++]=s);for(;l<t;)s=f-((u=f+a)-(i=u-f))+(a-i),a=e[++l],f=u,0!==s&&(o[d++]=s);for(;b<n;)s=f-((u=f+c)-(i=u-f))+(c-i),c=r[++b],f=u,0!==s&&(o[d++]=s);return 0===f&&0!==d||(o[d++]=f),d}function o(t,n,r,o){let f,u,s,i,a,c,l,b,d,h,M;M=r-(h=(l=e*r)-(l-r));let p=n[0],y=0;0!==(s=(d=p-(b=(l=e*p)-(l-p)))*M-((f=p*r)-b*h-d*h-b*M))&&(o[y++]=s);for(let x=1;x<t;x++)0!==(s=f-((u=f+(a=(d=(p=n[x])-(b=(l=e*p)-(l-p)))*M-((i=p*r)-b*h-d*h-b*M)))-(c=u-f))+(a-c))&&(o[y++]=s),0!==(s=u-((f=i+u)-i))&&(o[y++]=s);return 0===f&&0!==y||(o[y++]=f),y}function f(t){return new Float64Array(t)}const u=7771561172376103e-31,s=3330669073875473e-31,i=32047474274603644e-47,a=f(4),c=f(4),l=f(4),b=f(4),d=f(4),h=f(4),M=f(4),p=f(4),y=f(4),x=f(8),j=f(8),m=f(8),_=f(4),v=f(8),w=f(8),A=f(8),F=f(12);let O=f(192),P=f(192);function g(t,e,n){t=r(t,O,e,n,P);const o=O;return O=P,P=o,t}function k(t,n,r,o,f,u,s,i){let a,c,l,b,d,h,M,p,y,x,j,m,_,v,w;return 0===t?0===n?(s[0]=0,i[0]=0,1):(x=(w=-n)*r,b=w-(l=(c=e*w)-(c-w)),h=r-(d=(c=e*r)-(c-r)),s[0]=b*h-(x-l*d-b*d-l*h),s[1]=x,x=n*f,b=n-(l=(c=e*n)-(c-n)),h=f-(d=(c=e*f)-(c-f)),i[0]=b*h-(x-l*d-b*d-l*h),i[1]=x,2):0===n?(x=t*o,b=t-(l=(c=e*t)-(c-t)),h=o-(d=(c=e*o)-(c-o)),s[0]=b*h-(x-l*d-b*d-l*h),s[1]=x,x=(w=-t)*u,b=w-(l=(c=e*w)-(c-w)),h=u-(d=(c=e*u)-(c-u)),i[0]=b*h-(x-l*d-b*d-l*h),i[1]=x,2):(a=(j=(b=t-(l=(c=e*t)-(c-t)))*(h=o-(d=(c=e*o)-(c-o)))-((x=t*o)-l*d-b*d-l*h))-(M=j-(_=(b=n-(l=(c=e*n)-(c-n)))*(h=r-(d=(c=e*r)-(c-r)))-((m=n*r)-l*d-b*d-l*h))),s[0]=j-(M+a)+(a-_),a=(y=x-((p=x+M)-(a=p-x))+(M-a))-(M=y-m),s[1]=y-(M+a)+(a-m),a=(v=p+M)-p,s[2]=p-(v-a)+(M-a),s[3]=v,a=(j=(b=n-(l=(c=e*n)-(c-n)))*(h=f-(d=(c=e*f)-(c-f)))-((x=n*f)-l*d-b*d-l*h))-(M=j-(_=(b=t-(l=(c=e*t)-(c-t)))*(h=u-(d=(c=e*u)-(c-u)))-((m=t*u)-l*d-b*d-l*h))),i[0]=j-(M+a)+(a-_),a=(y=x-((p=x+M)-(a=p-x))+(M-a))-(M=y-m),i[1]=y-(M+a)+(a-m),a=(v=p+M)-p,i[2]=p-(v-a)+(M-a),i[3]=v,4)}function q(t,n,r,o,f){let u,s,i,a,c,l,b,d,h,M,p,y,x;return y=(a=n-(i=(s=e*n)-(s-n)))*(l=r-(c=(s=e*r)-(s-r)))-((p=n*r)-i*c-a*c-i*l),l=o-(c=(s=e*o)-(s-o)),b=y*o,a=y-(i=(s=e*y)-(s-y)),_[0]=a*l-(b-i*c-a*c-i*l),u=(h=b+(M=(a=p-(i=(s=e*p)-(s-p)))*l-((d=p*o)-i*c-a*c-i*l)))-b,_[1]=b-(h-u)+(M-u),x=d+h,_[2]=h-(x-d),_[3]=x,t=g(t,4,_),0!==f&&(l=f-(c=(s=e*f)-(s-f)),b=y*f,a=y-(i=(s=e*y)-(s-y)),_[0]=a*l-(b-i*c-a*c-i*l),u=(h=b+(M=(a=p-(i=(s=e*p)-(s-p)))*l-((d=p*f)-i*c-a*c-i*l)))-b,_[1]=b-(h-u)+(M-u),x=d+h,_[2]=h-(x-d),_[3]=x,t=g(t,4,_)),t}t.orient3d=function(t,f,_,P,z,B,C,D,E,G,H,I){const J=t-G,K=P-G,L=C-G,N=f-H,Q=z-H,R=D-H,S=_-I,T=B-I,U=E-I,V=K*R,W=L*Q,X=L*N,Y=J*R,Z=J*Q,$=K*N,tt=S*(V-W)+T*(X-Y)+U*(Z-$),et=(Math.abs(V)+Math.abs(W))*Math.abs(S)+(Math.abs(X)+Math.abs(Y))*Math.abs(T)+(Math.abs(Z)+Math.abs($))*Math.abs(U),nt=u*et;return tt>nt||-tt>nt?tt:function(t,f,u,_,P,z,B,C,D,E,G,H,I){let J,K,L,N,Q,R,S,T,U,V,W,X,Y,Z,$,tt,et,nt,rt,ot,ft,ut,st,it;const at=t-E,ct=_-E,lt=B-E,bt=f-G,dt=P-G,ht=C-G,Mt=u-H,pt=z-H,yt=D-H;W=(ft=(Z=ct-(Y=(X=e*ct)-(X-ct)))*(tt=ht-($=(X=e*ht)-(X-ht)))-((ot=ct*ht)-Y*$-Z*$-Y*tt))-(et=ft-(st=(Z=lt-(Y=(X=e*lt)-(X-lt)))*(tt=dt-($=(X=e*dt)-(X-dt)))-((ut=lt*dt)-Y*$-Z*$-Y*tt))),a[0]=ft-(et+W)+(W-st),W=(rt=ot-((nt=ot+et)-(W=nt-ot))+(et-W))-(et=rt-ut),a[1]=rt-(et+W)+(W-ut),W=(it=nt+et)-nt,a[2]=nt-(it-W)+(et-W),a[3]=it,W=(ft=(Z=lt-(Y=(X=e*lt)-(X-lt)))*(tt=bt-($=(X=e*bt)-(X-bt)))-((ot=lt*bt)-Y*$-Z*$-Y*tt))-(et=ft-(st=(Z=at-(Y=(X=e*at)-(X-at)))*(tt=ht-($=(X=e*ht)-(X-ht)))-((ut=at*ht)-Y*$-Z*$-Y*tt))),c[0]=ft-(et+W)+(W-st),W=(rt=ot-((nt=ot+et)-(W=nt-ot))+(et-W))-(et=rt-ut),c[1]=rt-(et+W)+(W-ut),W=(it=nt+et)-nt,c[2]=nt-(it-W)+(et-W),c[3]=it,W=(ft=(Z=at-(Y=(X=e*at)-(X-at)))*(tt=dt-($=(X=e*dt)-(X-dt)))-((ot=at*dt)-Y*$-Z*$-Y*tt))-(et=ft-(st=(Z=ct-(Y=(X=e*ct)-(X-ct)))*(tt=bt-($=(X=e*bt)-(X-bt)))-((ut=ct*bt)-Y*$-Z*$-Y*tt))),l[0]=ft-(et+W)+(W-st),W=(rt=ot-((nt=ot+et)-(W=nt-ot))+(et-W))-(et=rt-ut),l[1]=rt-(et+W)+(W-ut),W=(it=nt+et)-nt,l[2]=nt-(it-W)+(et-W),l[3]=it;let xt=function(t,e){let n=e[0];for(let r=1;r<t;r++)n+=e[r];return n}(J=r(r(o(4,a,Mt,v),v,o(4,c,pt,w),w,A),A,o(4,l,yt,v),v,O),O),jt=s*I;if(xt>=jt||-xt>=jt)return xt;if(K=t-(at+(W=t-at))+(W-E),L=_-(ct+(W=_-ct))+(W-E),N=B-(lt+(W=B-lt))+(W-E),Q=f-(bt+(W=f-bt))+(W-G),R=P-(dt+(W=P-dt))+(W-G),S=C-(ht+(W=C-ht))+(W-G),T=u-(Mt+(W=u-Mt))+(W-H),U=z-(pt+(W=z-pt))+(W-H),V=D-(yt+(W=D-yt))+(W-H),0===K&&0===L&&0===N&&0===Q&&0===R&&0===S&&0===T&&0===U&&0===V)return xt;if(jt=i*I+n*Math.abs(xt),(xt+=Mt*(ct*S+ht*L-(dt*N+lt*R))+T*(ct*ht-dt*lt)+pt*(lt*Q+bt*N-(ht*K+at*S))+U*(lt*bt-ht*at)+yt*(at*R+dt*K-(bt*L+ct*Q))+V*(at*dt-bt*ct))>=jt||-xt>=jt)return xt;const mt=k(K,Q,ct,dt,lt,ht,b,d),_t=k(L,R,lt,ht,at,bt,h,M),vt=k(N,S,at,bt,ct,dt,p,y),wt=r(_t,h,vt,y,x);J=g(J,o(wt,x,Mt,A),A);const At=r(vt,p,mt,d,j);J=g(J,o(At,j,pt,A),A);const Ft=r(mt,b,_t,M,m);return J=g(J,o(Ft,m,yt,A),A),0!==T&&(J=g(J,o(4,a,T,F),F),J=g(J,o(wt,x,T,A),A)),0!==U&&(J=g(J,o(4,c,U,F),F),J=g(J,o(At,j,U,A),A)),0!==V&&(J=g(J,o(4,l,V,F),F),J=g(J,o(Ft,m,V,A),A)),0!==K&&(0!==R&&(J=q(J,K,R,yt,V)),0!==S&&(J=q(J,-K,S,pt,U))),0!==L&&(0!==S&&(J=q(J,L,S,Mt,T)),0!==Q&&(J=q(J,-L,Q,yt,V))),0!==N&&(0!==Q&&(J=q(J,N,Q,pt,U)),0!==R&&(J=q(J,-N,R,Mt,T))),O[J-1]}(t,f,_,P,z,B,C,D,E,G,H,I,et)},t.orient3dfast=function(t,e,n,r,o,f,u,s,i,a,c,l){const b=e-c,d=o-c,h=s-c,M=n-l,p=f-l,y=i-l;return(t-a)*(d*y-p*h)+(r-a)*(h*M-y*b)+(u-a)*(b*p-M*d)},Object.defineProperty(t,"__esModule",{value:!0})});
