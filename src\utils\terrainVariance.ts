/**
 * 地形方差图处理工具
 * 基于地形方差图的蓝色通道计算动态地形夸张度
 */

import mapboxgl from 'mapbox-gl'

export interface TerrainVarianceConfig {
  imagePath: string
  imageSize: number
  defaultExaggeration: number
  maxExaggeration: number
  minExaggeration: number
}

export class TerrainVarianceProcessor {
  private canvas: HTMLCanvasElement | null = null
  private context: CanvasRenderingContext2D | null = null
  private image: HTMLImageElement | null = null
  private isLoaded = false
  private config: TerrainVarianceConfig

  constructor(config: TerrainVarianceConfig) {
    this.config = config
  }

  /**
   * 初始化地形方差图处理器
   */
  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      // 创建图像对象
      this.image = new Image()
      
      this.image.onload = () => {
        try {
          // 创建 Canvas 来处理地形变化图像
          this.canvas = document.createElement('canvas')
          this.canvas.width = this.image!.width
          this.canvas.height = this.image!.height
          
          // 获取 Canvas 上下文
          this.context = this.canvas.getContext('2d')
          if (!this.context) {
            throw new Error('无法获取 Canvas 上下文')
          }
          
          // 将图像绘制到 Canvas
          this.context.drawImage(this.image!, 0, 0)
          
          this.isLoaded = true
          console.log('地形方差图加载完成:', {
            width: this.canvas.width,
            height: this.canvas.height,
            path: this.config.imagePath
          })
          
          resolve()
        } catch (error) {
          reject(error)
        }
      }

      this.image.onerror = () => {
        reject(new Error(`地形方差图加载失败: ${this.config.imagePath}`))
      }

      // 开始加载图像
      this.image.src = this.config.imagePath
    })
  }

  /**
   * 根据地理坐标计算地形夸张度
   */
  calculateExaggeration(lng: number, lat: number): number {
    if (!this.isLoaded || !this.context || !this.canvas) {
      return this.config.defaultExaggeration
    }

    try {
      // 将地理坐标转换为墨卡托坐标
      const mercatorCoord = mapboxgl.MercatorCoordinate.fromLngLat([lng, lat])
      
      // 将墨卡托坐标映射到图像像素坐标
      const pixelX = Math.floor(mercatorCoord.x * this.canvas.width)
      const pixelY = Math.floor(mercatorCoord.y * this.canvas.height)
      
      // 确保坐标在有效范围内
      const clampedX = Math.max(0, Math.min(pixelX, this.canvas.width - 1))
      const clampedY = Math.max(0, Math.min(pixelY, this.canvas.height - 1))
      
      // 获取像素数据（1x1 区域）
      const imageData = this.context.getImageData(clampedX, clampedY, 1, 1)
      
      // 提取蓝色通道值（地形变化值）
      const blueChannel = imageData.data[2] // 蓝色通道索引为 2
      
      // 将 0-255 范围归一化为 0-1
      const variance = blueChannel / 255
      
      // 根据地形变化值计算夸张度
      // variance = 0: 地形变化最大（山地）-> 最大夸张度
      // variance = 1: 地形变化最小（平原）-> 最小夸张度
      const exaggeration = this.config.minExaggeration + (1 - variance) * 
        (this.config.maxExaggeration - this.config.minExaggeration)
      
      return Math.max(this.config.minExaggeration, 
        Math.min(this.config.maxExaggeration, exaggeration))
      
    } catch (error) {
      console.error('计算地形夸张度失败:', error)
      return this.config.defaultExaggeration
    }
  }

  /**
   * 获取指定位置的地形变化值（0-1）
   */
  getVarianceValue(lng: number, lat: number): number {
    if (!this.isLoaded || !this.context || !this.canvas) {
      return 0.5 // 默认中等变化值
    }

    try {
      const mercatorCoord = mapboxgl.MercatorCoordinate.fromLngLat([lng, lat])
      const pixelX = Math.floor(mercatorCoord.x * this.canvas.width)
      const pixelY = Math.floor(mercatorCoord.y * this.canvas.height)
      
      const clampedX = Math.max(0, Math.min(pixelX, this.canvas.width - 1))
      const clampedY = Math.max(0, Math.min(pixelY, this.canvas.height - 1))
      
      const imageData = this.context.getImageData(clampedX, clampedY, 1, 1)
      return imageData.data[2] / 255
      
    } catch (error) {
      console.error('获取地形变化值失败:', error)
      return 0.5
    }
  }

  /**
   * 获取地形类型描述
   */
  getTerrainType(variance: number): string {
    if (variance < 0.3) {
      return '高山地区'
    } else if (variance < 0.7) {
      return '丘陵地区'
    } else {
      return '平原地区'
    }
  }

  /**
   * 批量计算多个点的夸张度（用于轨迹优化）
   */
  calculateExaggerationForTrack(points: Array<{lng: number, lat: number}>): number[] {
    return points.map(point => this.calculateExaggeration(point.lng, point.lat))
  }

  /**
   * 获取轨迹的平均夸张度
   */
  getAverageExaggerationForTrack(points: Array<{lng: number, lat: number}>): number {
    if (points.length === 0) return this.config.defaultExaggeration
    
    const exaggerations = this.calculateExaggerationForTrack(points)
    const sum = exaggerations.reduce((acc, val) => acc + val, 0)
    return sum / exaggerations.length
  }

  /**
   * 检查是否已加载
   */
  get loaded(): boolean {
    return this.isLoaded
  }

  /**
   * 获取图像信息
   */
  getImageInfo() {
    return {
      loaded: this.isLoaded,
      width: this.canvas?.width || 0,
      height: this.canvas?.height || 0,
      path: this.config.imagePath
    }
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.canvas = null
    this.context = null
    this.image = null
    this.isLoaded = false
  }
}

/**
 * 创建默认的地形方差处理器
 */
export function createTerrainVarianceProcessor(): TerrainVarianceProcessor {
  const config: TerrainVarianceConfig = {
    imagePath: '/dem_std_zoom_4_512.png',
    imageSize: 512,
    defaultExaggeration: 1.35,
    maxExaggeration: 1.7,
    minExaggeration: 1.0
  }
  
  return new TerrainVarianceProcessor(config)
}
