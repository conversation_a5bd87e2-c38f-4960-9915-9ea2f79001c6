/**
 * 轨迹和运动数据类型定义
 */

export interface TrackPoint {
  lat: number
  lng: number
  elevation?: number
  timestamp?: number
  speed?: number
  heartRate?: number
}

export interface Location {
  lat: number
  lng: number
  name?: string
}

export interface AnimationConfig {
  duration: number
  fps: number
  totalFrames: number
  cameraSettings: {
    followTrack: boolean
    smoothing: number
    heightOffset: number
    lookAhead: number
  }
}

export interface RenderingConfig {
  trackColor: {
    start: string
    end: string
    encoding: 'speed' | 'heartrate' | 'elevation'
  }
  lineWidth: number
  opacity: number
  shadows: boolean
}

export interface TerrainConfig {
  enabled: boolean
  exaggeration: number
  source: string
  varianceImageUrl?: string
}

export interface WorkoutData {
  id: string
  name: string
  type: string
  date: string
  duration: number
  distance: number
  elevationGain: number
  elevationLoss: number
  startLocation: Location
  endLocation: Location
  animation: AnimationConfig
  rendering: RenderingConfig
  terrain: TerrainConfig
}

export interface SensorData {
  timestamp: number
  type: 'altitude' | 'heartrate' | 'speed' | 'cadence'
  value: number
}

export interface ProcessedTrackData {
  points: TrackPoint[]
  bounds: {
    north: number
    south: number
    east: number
    west: number
  }
  center: {
    lat: number
    lng: number
  }
  totalDistance: number
  totalTime: number
  elevationProfile: {
    min: number
    max: number
    gain: number
    loss: number
  }
}

export interface CameraKeyframe {
  position: [number, number, number]
  target: [number, number, number]
  timestamp: number
  progress: number
}

export interface AnimationState {
  isPlaying: boolean
  isPaused: boolean
  currentTime: number
  progress: number
  duration: number
  speed: number
}

export interface WebGLRenderData {
  positions: Float32Array
  colors: Float32Array
  indices: Uint16Array
  vertexCount: number
  indexCount: number
}
