const require_utils = require('./utils-BIvt10am.cjs');
const node_fs = require_utils.__toESM(require("node:fs"));
const node_process = require_utils.__toESM(require("node:process"));
const node_path = require_utils.__toESM(require("node:path"));
const debug = require_utils.__toESM(require("debug"));
const local_pkg = require_utils.__toESM(require("local-pkg"));
const mlly = require_utils.__toESM(require("mlly"));

//#region src/core/resolvers/antdv.ts
const matchComponents$3 = [
	{
		pattern: /^Affix/,
		styleDir: "affix"
	},
	{
		pattern: /^Avatar/,
		styleDir: "avatar"
	},
	{
		pattern: /^AutoComplete/,
		styleDir: "auto-complete"
	},
	{
		pattern: /^Alert/,
		styleDir: "alert"
	},
	{
		pattern: /^Anchor/,
		styleDir: "anchor"
	},
	{
		pattern: /^App/,
		styleDir: "app"
	},
	{
		pattern: /^Badge/,
		styleDir: "badge"
	},
	{
		pattern: /^Breadcrumb/,
		styleDir: "breadcrumb"
	},
	{
		pattern: /^Button/,
		styleDir: "button"
	},
	{
		pattern: /^Checkbox/,
		styleDir: "checkbox"
	},
	{
		pattern: /^Calendar/,
		styleDir: "calendar"
	},
	{
		pattern: /^Card/,
		styleDir: "card"
	},
	{
		pattern: /^Carousel/,
		styleDir: "carousel"
	},
	{
		pattern: /^Collapse/,
		styleDir: "collapse"
	},
	{
		pattern: /^Comment/,
		styleDir: "comment"
	},
	{
		pattern: /^Descriptions/,
		styleDir: "descriptions"
	},
	{
		pattern: /^RangePicker|^WeekPicker|^MonthPicker/,
		styleDir: "date-picker"
	},
	{
		pattern: /^Divider/,
		styleDir: "divider"
	},
	{
		pattern: /^Drawer/,
		styleDir: "drawer"
	},
	{
		pattern: /^Dropdown/,
		styleDir: "dropdown"
	},
	{
		pattern: /^Empty/,
		styleDir: "empty"
	},
	{
		pattern: /^Flex/,
		styleDir: "flex"
	},
	{
		pattern: /^FloatButton/,
		styleDir: "float-button"
	},
	{
		pattern: /^Form/,
		styleDir: "form"
	},
	{
		pattern: /^Grid/,
		styleDir: "grid"
	},
	{
		pattern: /^InputNumber/,
		styleDir: "input-number"
	},
	{
		pattern: /^Input|^Textarea/,
		styleDir: "input"
	},
	{
		pattern: /^Statistic/,
		styleDir: "statistic"
	},
	{
		pattern: /^CheckableTag/,
		styleDir: "tag"
	},
	{
		pattern: /^TimeRangePicker/,
		styleDir: "time-picker"
	},
	{
		pattern: /^Layout/,
		styleDir: "layout"
	},
	{
		pattern: /^Menu|^SubMenu/,
		styleDir: "menu"
	},
	{
		pattern: /^Table/,
		styleDir: "table"
	},
	{
		pattern: /^TimePicker|^TimeRangePicker/,
		styleDir: "time-picker"
	},
	{
		pattern: /^Radio/,
		styleDir: "radio"
	},
	{
		pattern: /^Image/,
		styleDir: "image"
	},
	{
		pattern: /^List/,
		styleDir: "list"
	},
	{
		pattern: /^Tab/,
		styleDir: "tabs"
	},
	{
		pattern: /^Mentions/,
		styleDir: "mentions"
	},
	{
		pattern: /^Step/,
		styleDir: "steps"
	},
	{
		pattern: /^Skeleton/,
		styleDir: "skeleton"
	},
	{
		pattern: /^Select/,
		styleDir: "select"
	},
	{
		pattern: /^TreeSelect/,
		styleDir: "tree-select"
	},
	{
		pattern: /^Tree|^DirectoryTree/,
		styleDir: "tree"
	},
	{
		pattern: /^Typography/,
		styleDir: "typography"
	},
	{
		pattern: /^Timeline/,
		styleDir: "timeline"
	},
	{
		pattern: /^Upload/,
		styleDir: "upload"
	},
	{
		pattern: /^Qrcode/,
		styleDir: "qrcode"
	},
	{
		pattern: /^Space/,
		styleDir: "space"
	}
];
function getStyleDir(compName) {
	let styleDir;
	const total = matchComponents$3.length;
	for (let i = 0; i < total; i++) {
		const matcher = matchComponents$3[i];
		if (compName.match(matcher.pattern)) {
			styleDir = matcher.styleDir;
			break;
		}
	}
	if (!styleDir) styleDir = require_utils.kebabCase(compName);
	return styleDir;
}
function getSideEffects$8(compName, options) {
	const { importStyle = true, importLess = false } = options;
	if (!importStyle) return;
	const lib = options.cjs ? "lib" : "es";
	const packageName = (options === null || options === void 0 ? void 0 : options.packageName) || "ant-design-vue";
	if (importStyle === "less" || importStyle === "css-in-js" || importLess) {
		const styleDir = getStyleDir(compName);
		return `${packageName}/${lib}/${styleDir}/style`;
	} else {
		const styleDir = getStyleDir(compName);
		return `${packageName}/${lib}/${styleDir}/style/css`;
	}
}
const primitiveNames = [
	"Affix",
	"Anchor",
	"AnchorLink",
	"AutoComplete",
	"AutoCompleteOptGroup",
	"AutoCompleteOption",
	"Alert",
	"Avatar",
	"AvatarGroup",
	"BackTop",
	"Badge",
	"BadgeRibbon",
	"Breadcrumb",
	"BreadcrumbItem",
	"BreadcrumbSeparator",
	"Button",
	"ButtonGroup",
	"Calendar",
	"Card",
	"CardGrid",
	"CardMeta",
	"Collapse",
	"CollapsePanel",
	"Carousel",
	"Cascader",
	"Checkbox",
	"CheckboxGroup",
	"Col",
	"Comment",
	"ConfigProvider",
	"DatePicker",
	"MonthPicker",
	"WeekPicker",
	"RangePicker",
	"QuarterPicker",
	"Descriptions",
	"DescriptionsItem",
	"Divider",
	"Dropdown",
	"DropdownButton",
	"Drawer",
	"Empty",
	"Form",
	"FormItem",
	"FormItemRest",
	"Grid",
	"Input",
	"InputGroup",
	"InputPassword",
	"InputSearch",
	"Textarea",
	"Image",
	"ImagePreviewGroup",
	"InputNumber",
	"Layout",
	"LayoutHeader",
	"LayoutSider",
	"LayoutFooter",
	"LayoutContent",
	"List",
	"ListItem",
	"ListItemMeta",
	"Menu",
	"MenuDivider",
	"MenuItem",
	"MenuItemGroup",
	"SubMenu",
	"Mentions",
	"MentionsOption",
	"Modal",
	"Statistic",
	"StatisticCountdown",
	"PageHeader",
	"Pagination",
	"Popconfirm",
	"Popover",
	"Progress",
	"Radio",
	"RadioButton",
	"RadioGroup",
	"Rate",
	"Result",
	"Row",
	"Select",
	"SelectOptGroup",
	"SelectOption",
	"Skeleton",
	"SkeletonButton",
	"SkeletonAvatar",
	"SkeletonInput",
	"SkeletonImage",
	"Slider",
	"Space",
	"Spin",
	"Steps",
	"Step",
	"Switch",
	"Table",
	"TableColumn",
	"TableColumnGroup",
	"TableSummary",
	"TableSummaryRow",
	"TableSummaryCell",
	"Transfer",
	"Tree",
	"TreeNode",
	"DirectoryTree",
	"TreeSelect",
	"TreeSelectNode",
	"Tabs",
	"TabPane",
	"Tag",
	"CheckableTag",
	"TimePicker",
	"TimeRangePicker",
	"Timeline",
	"TimelineItem",
	"Tooltip",
	"Typography",
	"TypographyLink",
	"TypographyParagraph",
	"TypographyText",
	"TypographyTitle",
	"Upload",
	"UploadDragger",
	"LocaleProvider",
	"FloatButton",
	"FloatButtonGroup",
	"Qrcode",
	"Watermark",
	"Segmented",
	"Tour",
	"SpaceCompact",
	"StyleProvider",
	"Flex",
	"App"
];
let antdvNames;
function genAntdNames(primitiveNames$1) {
	antdvNames = new Set(primitiveNames$1);
}
genAntdNames(primitiveNames);
function isAntdv(compName) {
	return antdvNames.has(compName);
}
function getImportName(compName) {
	if (compName === "Qrcode") return "QRCode";
	else if (compName === "SpaceCompact") return "Compact";
	return compName;
}
/**
* Resolver for Ant Design Vue
*
* Requires ant-design-vue@v2.2.0-beta.6 or later
*
* See https://github.com/antfu/unplugin-vue-components/issues/26#issuecomment-789767941 for more details
*
* <AUTHOR>
* @link https://antdv.com/
*/
function AntDesignVueResolver(options = {}) {
	const originPrefix = options.prefix ?? "A";
	return {
		type: "component",
		resolve: (name) => {
			var _options$exclude;
			if (options.resolveIcons && name.match(/(Outlined|Filled|TwoTone)$/)) return {
				name,
				from: "@ant-design/icons-vue"
			};
			const [compName, prefix] = [name.slice(originPrefix.length), name.slice(0, originPrefix.length)];
			if (prefix === originPrefix && isAntdv(compName) && !(options === null || options === void 0 || (_options$exclude = options.exclude) === null || _options$exclude === void 0 ? void 0 : _options$exclude.includes(compName))) {
				const { cjs = false, packageName = "ant-design-vue" } = options;
				const path = `${packageName}/${cjs ? "lib" : "es"}`;
				return {
					name: getImportName(compName),
					from: path,
					sideEffects: getSideEffects$8(compName, options)
				};
			}
		}
	};
}

//#endregion
//#region src/core/resolvers/arco.ts
const debug$1 = (0, debug.default)("unplugin-vue-components:resolvers:arco");
const matchComponents$2 = [
	{
		pattern: /^AnchorLink$/,
		componentDir: "anchor"
	},
	{
		pattern: /^AvatarGroup$/,
		componentDir: "avatar"
	},
	{
		pattern: /^BreadcrumbItem$/,
		componentDir: "breadcrumb"
	},
	{
		pattern: /^ButtonGroup$/,
		componentDir: "button"
	},
	{
		pattern: /^(CardMeta|CardGrid)$/,
		componentDir: "card"
	},
	{
		pattern: /^CarouselItem$/,
		componentDir: "carousel"
	},
	{
		pattern: /^CascaderPanel$/,
		componentDir: "cascader"
	},
	{
		pattern: /^CheckboxGroup$/,
		componentDir: "checkbox"
	},
	{
		pattern: /^CollapseItem$/,
		componentDir: "collapse"
	},
	{
		pattern: /^(WeekPicker|MonthPicker|YearPicker|QuarterPicker|RangePicker)$/,
		componentDir: "date-picker"
	},
	{
		pattern: /^DescriptionsItem$/,
		componentDir: "descriptions"
	},
	{
		pattern: /^(Doption|Dgroup|Dsubmenu|DropdownButton)$/,
		componentDir: "dropdown"
	},
	{
		pattern: /^FormItem$/,
		componentDir: "form"
	},
	{
		pattern: /^(Col|Row|GridItem)$/,
		componentDir: "grid"
	},
	{
		pattern: /^(ImagePreview|ImagePreviewGroup)$/,
		componentDir: "image"
	},
	{
		pattern: /^(InputGroup|InputSearch|InputPassword)$/,
		componentDir: "input"
	},
	{
		pattern: /^(LayoutHeader|LayoutContent|LayoutFooter|LayoutSider)$/,
		componentDir: "layout"
	},
	{
		pattern: /^(ListItem|ListItemMeta)$/,
		componentDir: "list"
	},
	{
		pattern: /^(MenuItem|MenuItemGroup|SubMenu)$/,
		componentDir: "menu"
	},
	{
		pattern: /^RadioGroup$/,
		componentDir: "radio"
	},
	{
		pattern: /^(Option|Optgroup)$/,
		componentDir: "select"
	},
	{
		pattern: /^(SkeletonLine|SkeletonShape)$/,
		componentDir: "skeleton"
	},
	{
		pattern: /^Countdown$/,
		componentDir: "statistic"
	},
	{
		pattern: /^Step$/,
		componentDir: "steps"
	},
	{
		pattern: /^(Thead|Td|Th|Tr|Tbody|TableColumn)$/,
		componentDir: "table"
	},
	{
		pattern: /^TagGroup$/,
		componentDir: "tag"
	},
	{
		pattern: /^TabPane$/,
		componentDir: "tabs"
	},
	{
		pattern: /^TimelineItem$/,
		componentDir: "timeline"
	},
	{
		pattern: /^(TypographyParagraph|TypographyTitle|TypographyText)$/,
		componentDir: "typography"
	}
];
function getComponentStyleDir(importName, importStyle) {
	if (["ConfigProvider", "Icon"].includes(importName)) return void 0;
	let componentDir = require_utils.kebabCase(importName);
	for (const item of matchComponents$2) if (item.pattern.test(importName)) {
		componentDir = item.componentDir;
		break;
	}
	if (importStyle === "less") return `@arco-design/web-vue/es/${componentDir}/style/index.js`;
	if (importStyle === "css" || importStyle) return `@arco-design/web-vue/es/${componentDir}/style/css.js`;
}
function canResolveIcons(options) {
	if (options === void 0) return false;
	if (typeof options === "boolean") return options;
	else return options.enable;
}
function getResolveIconPrefix(options) {
	if (canResolveIcons(options)) if (typeof options === "boolean" && options) return "";
	else if (options.enable) return options.iconPrefix ?? "";
	else return "";
	return "";
}
/**
* Resolver for Arco Design Vue
*
* Requires arco-design/web-vue@2.11.0 or later
*
* <AUTHOR>
* @link https://arco.design/ for arco-design
*
*/
function ArcoResolver(options = {}) {
	return {
		type: "component",
		resolve: (name) => {
			if (canResolveIcons(options.resolveIcons)) {
				const iconPrefix = require_utils.pascalCase(getResolveIconPrefix(options.resolveIcons));
				const newNameRegexp = new RegExp(`^${iconPrefix}Icon`);
				if (newNameRegexp.test(name)) {
					debug$1("found icon component name %s", name);
					const rawComponentName = name.slice(iconPrefix.length);
					debug$1("found icon component raw name %s", rawComponentName);
					return {
						name: rawComponentName,
						as: name,
						from: "@arco-design/web-vue/es/icon"
					};
				}
			}
			if (name.match(/^A[A-Z]/) && !require_utils.isExclude(name, options.exclude)) {
				const importStyle = options.importStyle ?? "css";
				const importName = name.slice(1);
				const config = {
					name: importName,
					from: "@arco-design/web-vue"
				};
				if (options.sideEffect !== false) config.sideEffects = getComponentStyleDir(importName, importStyle);
				return config;
			}
		}
	};
}

//#endregion
//#region src/core/resolvers/bootstrap-vue.ts
const COMPONENT_ALIASES = {
	BBtn: "BButton",
	BBtnClose: "BButtonClose",
	BBtnGroup: "BButtonGroup",
	BBtnToolbar: "BButtonToolbar",
	BCheck: "BFormCheckbox",
	BCheckbox: "BFormCheckbox",
	BCheckboxGroup: "BFormCheckboxGroup",
	BCheckGroup: "BFormCheckboxGroup",
	BDatalist: "BFormDatalist",
	BDatepicker: "BFormDatepicker",
	BDd: "BDropdown",
	BDdDivider: "BDropdownDivider",
	BDdForm: "BDropdownForm",
	BDdGroup: "BDropdownGroup",
	BDdHeader: "BDropdownHeader",
	BDdItem: "BDropdownItem",
	BDdItemBtn: "BDropdownItemButton",
	BDdItemButton: "BDropdownItemButton",
	BDdText: "BDropdownText",
	BDropdownItemBtn: "BDropdownItemButton",
	BFile: "BFormFile",
	BInput: "BFormInput",
	BNavDd: "BNavItemDropdown",
	BNavDropdown: "BNavItemDropdown",
	BNavItemDd: "BNavItemDropdown",
	BNavToggle: "BNavbarToggle",
	BRadio: "BFormRadio",
	BRadioGroup: "BFormRadioGroup",
	BRating: "BFormRating",
	BSelect: "BFormSelect",
	BSelectOption: "BFormSelectOption",
	BSelectOptionGroup: "BFormSelectOptionGroup",
	BSpinbutton: "BFormSpinbutton",
	BTag: "BFormTag",
	BTags: "BFormTags",
	BTextarea: "BFormTextarea",
	BTimepicker: "BFormTimepicker"
};
/**
* Resolver for BootstrapVue
*
* @link https://github.com/bootstrap-vue/bootstrap-vue
*/
function BootstrapVueResolver(_options = {}) {
	const options = {
		directives: true,
		..._options
	};
	const resolvers = [{
		type: "component",
		resolve: (name) => {
			if (name.match(/^B[A-Z]/)) return {
				name: COMPONENT_ALIASES[name] || name,
				from: "bootstrap-vue"
			};
		}
	}];
	if (options.directives) resolvers.push({
		type: "directive",
		resolve: (name) => {
			if (name.match(/^B[A-Z]/)) return {
				name: `V${name}`,
				from: "bootstrap-vue"
			};
		}
	});
	return resolvers;
}
/**
* Resolver for BootstrapVueNext
*
* @link https://github.com/bootstrap-vue/bootstrap-vue-next
*
* @deprecated use `import { BootstrapVueNextResolver } from 'bootstrap-vue-next'` instead
*/
function BootstrapVueNextResolver(_options = {}) {
	const options = {
		directives: true,
		..._options
	};
	const resolvers = [{
		type: "component",
		resolve: (name) => {
			if (name.match(/^B[A-Z]/)) return {
				name,
				from: "bootstrap-vue-next"
			};
		}
	}];
	if (options.directives) resolvers.push({
		type: "directive",
		resolve: (name) => {
			if (name.match(/^B[A-Z]/)) return {
				name: `v${name}`,
				from: "bootstrap-vue-next"
			};
		}
	});
	return resolvers;
}
/**
* Resolver for legacy BootstrapVue3 apps
*
* @deprecated use BootstrapVueNextResolver with https://github.com/bootstrap-vue/bootstrap-vue-next
* @link https://www.npmjs.com/package/bootstrap-vue-3
*/
function BootstrapVue3Resolver(_options = {}) {
	const options = {
		directives: true,
		..._options
	};
	const resolvers = [{
		type: "component",
		resolve: (name) => {
			if (name.match(/^B[A-Z]/)) return {
				name,
				from: "bootstrap-vue-3"
			};
		}
	}];
	if (options.directives) resolvers.push({
		type: "directive",
		resolve: (name) => {
			if (name.match(/^B[A-Z]/)) return {
				name: `V${name}`,
				from: "bootstrap-vue-3"
			};
		}
	});
	return resolvers;
}

//#endregion
//#region src/core/resolvers/devui.ts
const LIB_NAME = "vue-devui";
const HARMLESS = ["ripple"];
function resolveDirectory(name, filename) {
	return `${LIB_NAME}/${name}/${filename}`;
}
function getSideEffects$7(name, filename) {
	if (HARMLESS.includes(name)) return;
	if (["row", "col"].includes(name)) return resolveDirectory("grid", filename);
	if ([
		"aside",
		"content",
		"footer",
		"header",
		"layout"
	].includes(name)) return resolveDirectory("layout", filename);
	if ([
		"overlay",
		"fixed-overlay",
		"flexible-overlay"
	].includes(name)) return resolveDirectory("overlay", filename);
	if ([
		"panel",
		"panel-header",
		"panel-body"
	].includes(name)) return resolveDirectory("panel", filename);
	if ([
		"menu",
		"menu-item",
		"sub-menu"
	].includes(name)) return resolveDirectory("menu", filename);
	if (["tabs", "tab"].includes(name)) return resolveDirectory("tabs", filename);
	if (["form", "form-item"].includes(name)) return resolveDirectory("form", filename);
	if (["collapse", "collapse-item"].includes(name)) return resolveDirectory("collapse", filename);
	if (["steps", "step"].includes(name)) return resolveDirectory("steps", filename);
	if ([
		"radio",
		"radio-group",
		"radio-button"
	].includes(name)) return resolveDirectory("radio", filename);
	if (["column"].includes(name)) return resolveDirectory("table", filename);
	if (["timeline-item"].includes(name)) return resolveDirectory("timeline", filename);
	if (["splitter-pane"].includes(name)) return resolveDirectory("splitter", filename);
	return resolveDirectory(name, filename);
}
function componentsResolver(name, { ssr }) {
	if (!name.match(/^D[A-Z]/)) return;
	const resolveId = require_utils.kebabCase(name = name.slice(1));
	return {
		name,
		sideEffects: getSideEffects$7(resolveId, "style.css"),
		from: getSideEffects$7(resolveId, `index.${ssr ? "umd" : "es"}.js`)
	};
}
function directivesResolver(name, { ssr }) {
	const resolveId = require_utils.kebabCase(name);
	return {
		name: `${name}Directive`,
		sideEffects: getSideEffects$7(resolveId, "style.css"),
		from: resolveDirectory(resolveId, `index.${ssr ? "umd" : "es"}.js`)
	};
}
function DevUiResolver(options = {}) {
	const config = {
		directives: true,
		importStyle: true,
		ssr: false,
		...options
	};
	const resolvers = [{
		type: "component",
		resolve: (name) => componentsResolver(name, config)
	}];
	if (config.directives) resolvers.push({
		type: "directive",
		resolve: (name) => directivesResolver(name, config)
	});
	return resolvers;
}

//#endregion
//#region node_modules/.pnpm/compare-versions@6.1.1/node_modules/compare-versions/lib/umd/index.js
var require_umd = require_utils.__commonJS({ "node_modules/.pnpm/compare-versions@6.1.1/node_modules/compare-versions/lib/umd/index.js"(exports, module) {
	(function(global, factory) {
		typeof exports === "object" && typeof module !== "undefined" ? factory(exports) : typeof define === "function" && define.amd ? define(["exports"], factory) : (global = typeof globalThis !== "undefined" ? globalThis : global || self, factory(global.compareVersions = {}));
	})(void 0, function(exports$1) {
		"use strict";
		const semver = /^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z\-]+(?:\.[\da-z\-]+)*))?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i;
		const validateAndParse = (version) => {
			if (typeof version !== "string") throw new TypeError("Invalid argument expected string");
			const match = version.match(semver);
			if (!match) throw new Error(`Invalid argument not valid semver ('${version}' received)`);
			match.shift();
			return match;
		};
		const isWildcard = (s) => s === "*" || s === "x" || s === "X";
		const tryParse = (v) => {
			const n = parseInt(v, 10);
			return isNaN(n) ? v : n;
		};
		const forceType = (a, b) => typeof a !== typeof b ? [String(a), String(b)] : [a, b];
		const compareStrings = (a, b) => {
			if (isWildcard(a) || isWildcard(b)) return 0;
			const [ap, bp] = forceType(tryParse(a), tryParse(b));
			if (ap > bp) return 1;
			if (ap < bp) return -1;
			return 0;
		};
		const compareSegments = (a, b) => {
			for (let i = 0; i < Math.max(a.length, b.length); i++) {
				const r = compareStrings(a[i] || "0", b[i] || "0");
				if (r !== 0) return r;
			}
			return 0;
		};
		/**
		* Compare [semver](https://semver.org/) version strings to find greater, equal or lesser.
		* This library supports the full semver specification, including comparing versions with different number of digits like `1.0.0`, `1.0`, `1`, and pre-release versions like `1.0.0-alpha`.
		* @param v1 - First version to compare
		* @param v2 - Second version to compare
		* @returns Numeric value compatible with the [Array.sort(fn) interface](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#Parameters).
		*/
		const compareVersions = (v1, v2) => {
			const n1 = validateAndParse(v1);
			const n2 = validateAndParse(v2);
			const p1 = n1.pop();
			const p2 = n2.pop();
			const r = compareSegments(n1, n2);
			if (r !== 0) return r;
			if (p1 && p2) return compareSegments(p1.split("."), p2.split("."));
			else if (p1 || p2) return p1 ? -1 : 1;
			return 0;
		};
		/**
		* Compare [semver](https://semver.org/) version strings using the specified operator.
		*
		* @param v1 First version to compare
		* @param v2 Second version to compare
		* @param operator Allowed arithmetic operator to use
		* @returns `true` if the comparison between the firstVersion and the secondVersion satisfies the operator, `false` otherwise.
		*
		* @example
		* ```
		* compare('10.1.8', '10.0.4', '>'); // return true
		* compare('10.0.1', '10.0.1', '='); // return true
		* compare('10.1.1', '10.2.2', '<'); // return true
		* compare('10.1.1', '10.2.2', '<='); // return true
		* compare('10.1.1', '10.2.2', '>='); // return false
		* ```
		*/
		const compare$2 = (v1, v2, operator) => {
			assertValidOperator(operator);
			const res = compareVersions(v1, v2);
			return operatorResMap[operator].includes(res);
		};
		const operatorResMap = {
			">": [1],
			">=": [0, 1],
			"=": [0],
			"<=": [-1, 0],
			"<": [-1],
			"!=": [-1, 1]
		};
		const allowedOperators = Object.keys(operatorResMap);
		const assertValidOperator = (op) => {
			if (typeof op !== "string") throw new TypeError(`Invalid operator type, expected string but got ${typeof op}`);
			if (allowedOperators.indexOf(op) === -1) throw new Error(`Invalid operator, expected one of ${allowedOperators.join("|")}`);
		};
		/**
		* Match [npm semver](https://docs.npmjs.com/cli/v6/using-npm/semver) version range.
		*
		* @param version Version number to match
		* @param range Range pattern for version
		* @returns `true` if the version number is within the range, `false` otherwise.
		*
		* @example
		* ```
		* satisfies('1.1.0', '^1.0.0'); // return true
		* satisfies('1.1.0', '~1.0.0'); // return false
		* ```
		*/
		const satisfies = (version, range) => {
			range = range.replace(/([><=]+)\s+/g, "$1");
			if (range.includes("||")) return range.split("||").some((r$1) => satisfies(version, r$1));
			else if (range.includes(" - ")) {
				const [a, b] = range.split(" - ", 2);
				return satisfies(version, `>=${a} <=${b}`);
			} else if (range.includes(" ")) return range.trim().replace(/\s{2,}/g, " ").split(" ").every((r$1) => satisfies(version, r$1));
			const m = range.match(/^([<>=~^]+)/);
			const op = m ? m[1] : "=";
			if (op !== "^" && op !== "~") return compare$2(version, range, op);
			const [v1, v2, v3, , vp] = validateAndParse(version);
			const [r1, r2, r3, , rp] = validateAndParse(range);
			const v = [
				v1,
				v2,
				v3
			];
			const r = [
				r1,
				r2 !== null && r2 !== void 0 ? r2 : "x",
				r3 !== null && r3 !== void 0 ? r3 : "x"
			];
			if (rp) {
				if (!vp) return false;
				if (compareSegments(v, r) !== 0) return false;
				if (compareSegments(vp.split("."), rp.split(".")) === -1) return false;
			}
			const nonZero = r.findIndex((v$1) => v$1 !== "0") + 1;
			const i = op === "~" ? 2 : nonZero > 1 ? nonZero : 1;
			if (compareSegments(v.slice(0, i), r.slice(0, i)) !== 0) return false;
			if (compareSegments(v.slice(i), r.slice(i)) === -1) return false;
			return true;
		};
		/**
		* Validate [semver](https://semver.org/) version strings.
		*
		* @param version Version number to validate
		* @returns `true` if the version number is a valid semver version number, `false` otherwise.
		*
		* @example
		* ```
		* validate('1.0.0-rc.1'); // return true
		* validate('1.0-rc.1'); // return false
		* validate('foo'); // return false
		* ```
		*/
		const validate = (version) => typeof version === "string" && /^[v\d]/.test(version) && semver.test(version);
		/**
		* Validate [semver](https://semver.org/) version strings strictly. Will not accept wildcards and version ranges.
		*
		* @param version Version number to validate
		* @returns `true` if the version number is a valid semver version number `false` otherwise
		*
		* @example
		* ```
		* validate('1.0.0-rc.1'); // return true
		* validate('1.0-rc.1'); // return false
		* validate('foo'); // return false
		* ```
		*/
		const validateStrict = (version) => typeof version === "string" && /^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/.test(version);
		exports$1.compare = compare$2;
		exports$1.compareVersions = compareVersions;
		exports$1.satisfies = satisfies;
		exports$1.validate = validate;
		exports$1.validateStrict = validateStrict;
	});
} });

//#endregion
//#region src/core/resolvers/element-plus.ts
var import_umd$1 = require_utils.__toESM(require_umd(), 1);
/**
* @deprecated
* @param partialName
* @param options
*/
function getSideEffectsLegacy(partialName, options) {
	const { importStyle } = options;
	if (!importStyle) return;
	if (importStyle === "sass") return ["element-plus/packages/theme-chalk/src/base.scss", `element-plus/packages/theme-chalk/src/${partialName}.scss`];
	else if (importStyle === true || importStyle === "css") return ["element-plus/lib/theme-chalk/base.css", `element-plus/lib/theme-chalk/el-${partialName}.css`];
}
function getSideEffects$6(dirName, options) {
	const { importStyle, ssr, nightly } = options;
	const themeFolder = nightly ? "@element-plus/nightly/theme-chalk" : "element-plus/theme-chalk";
	const esComponentsFolder = nightly ? "@element-plus/nightly/es/components" : "element-plus/es/components";
	if (importStyle === "sass") return ssr ? [`${themeFolder}/src/base.scss`, `${themeFolder}/src/${dirName}.scss`] : [`${esComponentsFolder}/base/style/index`, `${esComponentsFolder}/${dirName}/style/index`];
	else if (importStyle === true || importStyle === "css") return ssr ? [`${themeFolder}/base.css`, `${themeFolder}/el-${dirName}.css`] : [`${esComponentsFolder}/base/style/css`, `${esComponentsFolder}/${dirName}/style/css`];
}
function resolveComponent$1(name, options) {
	if (options.exclude && name.match(options.exclude)) return;
	if (!name.match(/^El[A-Z]/)) return;
	if (name.match(/^ElIcon.+/)) return {
		name: name.replace(/^ElIcon/, ""),
		from: "@element-plus/icons-vue"
	};
	const partialName = require_utils.kebabCase(name.slice(2));
	const { version, ssr, nightly } = options;
	if ((0, import_umd$1.compare)(version, "1.1.0-beta.1", ">=") || nightly) return {
		name,
		from: `${nightly ? "@element-plus/nightly" : "element-plus"}/${ssr ? "lib" : "es"}`,
		sideEffects: getSideEffects$6(partialName, options)
	};
	else if ((0, import_umd$1.compare)(version, "1.0.2-beta.28", ">=")) return {
		from: `element-plus/es/el-${partialName}`,
		sideEffects: getSideEffectsLegacy(partialName, options)
	};
	else return {
		from: `element-plus/lib/el-${partialName}`,
		sideEffects: getSideEffectsLegacy(partialName, options)
	};
}
function resolveDirective(name, options) {
	if (!options.directives) return;
	const directives$1 = {
		Loading: {
			importName: "ElLoadingDirective",
			styleName: "loading"
		},
		Popover: {
			importName: "ElPopoverDirective",
			styleName: "popover"
		},
		InfiniteScroll: {
			importName: "ElInfiniteScroll",
			styleName: "infinite-scroll"
		}
	};
	const directive = directives$1[name];
	if (!directive) return;
	const { version, ssr, nightly } = options;
	if ((0, import_umd$1.compare)(version, "1.1.0-beta.1", ">=") || nightly) return {
		name: directive.importName,
		from: `${nightly ? "@element-plus/nightly" : "element-plus"}/${ssr ? "lib" : "es"}`,
		sideEffects: getSideEffects$6(directive.styleName, options)
	};
}
const noStylesComponents = ["ElAutoResizer"];
/**
* Resolver for Element Plus
*
* See https://github.com/antfu/vite-plugin-components/pull/28 for more details
* See https://github.com/antfu/vite-plugin-components/issues/117 for more details
*
* <AUTHOR> @nabaonan @sxzz
* @link https://element-plus.org/ for element-plus
*
*/
function ElementPlusResolver(options = {}) {
	let optionsResolved;
	async function resolveOptions() {
		if (optionsResolved) return optionsResolved;
		optionsResolved = {
			ssr: false,
			version: await require_utils.getPkgVersion("element-plus", "2.2.2"),
			importStyle: "css",
			directives: true,
			exclude: void 0,
			noStylesComponents: options.noStylesComponents || [],
			nightly: false,
			...options
		};
		return optionsResolved;
	}
	return [{
		type: "component",
		resolve: async (name) => {
			const options$1 = await resolveOptions();
			if ([...options$1.noStylesComponents, ...noStylesComponents].includes(name)) return resolveComponent$1(name, {
				...options$1,
				importStyle: false
			});
			else return resolveComponent$1(name, options$1);
		}
	}, {
		type: "directive",
		resolve: async (name) => {
			return resolveDirective(name, await resolveOptions());
		}
	}];
}

//#endregion
//#region src/core/resolvers/element-ui.ts
function getSideEffects$5(partialName, options) {
	const { importStyle = "css" } = options;
	if (!importStyle) return;
	if (importStyle === "sass") return ["element-ui/packages/theme-chalk/src/base.scss", `element-ui/packages/theme-chalk/src/${partialName}.scss`];
	else return ["element-ui/lib/theme-chalk/base.css", `element-ui/lib/theme-chalk/${partialName}.css`];
}
/**
* Resolver for Element-UI
* @link https://element.eleme.cn/#/zh-CN
* @version @element-ui ^2.15.3, @vue ^2.6.14
* <AUTHOR>
*/
function ElementUiResolver(options = {}) {
	return {
		type: "component",
		resolve: (name) => {
			if (options.exclude && name.match(options.exclude)) return;
			if (/^El[A-Z]/.test(name)) {
				const compName = name.slice(2);
				const partialName = require_utils.kebabCase(compName);
				if (partialName === "collapse-transition") return { from: `element-ui/lib/transitions/${partialName}` };
				return {
					from: `element-ui/lib/${partialName}`,
					sideEffects: getSideEffects$5(partialName, options)
				};
			}
		}
	};
}

//#endregion
//#region src/core/resolvers/headless-ui.ts
const components$3 = [
	"Combobox",
	"ComboboxButton",
	"ComboboxInput",
	"ComboboxLabel",
	"ComboboxOption",
	"ComboboxOptions",
	"Dialog",
	"DialogDescription",
	"DialogOverlay",
	"DialogPanel",
	"DialogTitle",
	"Disclosure",
	"DisclosureButton",
	"DisclosurePanel",
	"FocusTrap",
	"Listbox",
	"ListboxButton",
	"ListboxLabel",
	"ListboxOption",
	"ListboxOptions",
	"Menu",
	"MenuButton",
	"MenuItem",
	"MenuItems",
	"Popover",
	"PopoverButton",
	"PopoverGroup",
	"PopoverOverlay",
	"PopoverPanel",
	"Portal",
	"PortalGroup",
	"RadioGroup",
	"RadioGroupDescription",
	"RadioGroupLabel",
	"RadioGroupOption",
	"Switch",
	"SwitchDescription",
	"SwitchGroup",
	"SwitchLabel",
	"Tab",
	"TabGroup",
	"TabList",
	"TabPanel",
	"TabPanels",
	"TransitionChild",
	"TransitionRoot"
];
/**
* Resolver for headlessui
*
* @link https://github.com/tailwindlabs/headlessui
*/
function HeadlessUiResolver(options = {}) {
	const { prefix = "" } = options;
	return {
		type: "component",
		resolve: (name) => {
			if (name.startsWith(prefix)) {
				const componentName = name.substring(prefix.length);
				if (components$3.includes(componentName)) return {
					name: componentName,
					from: "@headlessui/vue"
				};
			}
		}
	};
}

//#endregion
//#region src/core/resolvers/idux.ts
var import_umd = require_utils.__toESM(require_umd(), 1);
const specialComponents = {
	CdkClickOutside: "click-outside",
	CdkDraggable: "drag-drop",
	CdkResizable: "resize",
	CdkResizableHandle: "resize",
	CdkResizeObserver: "resize",
	CdkVirtualScroll: "scroll",
	IxAutoComplete: "auto-complete",
	IxBackTop: "back-top",
	IxCol: "grid",
	IxDatePicker: "date-picker",
	IxDateRangePicker: "date-picker",
	IxInputNumber: "input-number",
	IxLoadingBar: "loading-bar",
	IxLoadingBarProvider: "loading-bar",
	IxRow: "grid",
	IxTab: "tabs",
	IxTimePicker: "time-picker",
	IxTimeRangePicker: "time-picker",
	IxTreeSelect: "tree-select"
};
/**
* Resolver for `@idux/cdk`, `@idux/components` and ``@idux/pro``
*
* @link https://idux.site
*/
function IduxResolver(options = {}) {
	return {
		type: "component",
		resolve: async (name) => {
			const { importStyle, importStyleTheme, exclude = [], scope = "@idux" } = options;
			if (exclude.includes(name)) return;
			const packageName = getPackageName(name);
			if (!packageName) return;
			const resolvedVersion = await require_utils.getPkgVersion(`${scope}/${packageName}`, "2.0.0");
			let dirname = specialComponents[name];
			if (!dirname) {
				const nameIndex = packageName === "pro" ? 2 : 1;
				dirname = require_utils.kebabCase(name).split("-")[nameIndex];
			}
			const path = `${scope}/${packageName}/${dirname}`;
			const sideEffects = packageName === "cdk" ? void 0 : getSideEffects$4(resolvedVersion, path, importStyle, importStyleTheme);
			return {
				name,
				from: path,
				sideEffects
			};
		}
	};
}
function getPackageName(name) {
	let packageName;
	if (name.match(/^Cdk[A-Z]/)) packageName = "cdk";
	else if (name.match(/^IxPro[A-Z]/)) packageName = "pro";
	else if (name.match(/^Ix[A-Z]/)) packageName = "components";
	return packageName;
}
function getSideEffects$4(version, path, importStyle, importStyleTheme) {
	if (!importStyle) return;
	if ((0, import_umd.compare)(version, "2.0.0-beta.0", "<")) return getLegacySideEffects(path, importStyle, importStyleTheme);
	const styleRoot = `${path}/style`;
	const themeRoot = `${path}/theme`;
	const styleImport = `${styleRoot}/${importStyle === "css" ? "index_css" : "index"}`;
	if (!(0, local_pkg.resolveModule)(styleImport)) return;
	const themeImport = `${themeRoot}/${importStyleTheme}.css`;
	if (!importStyleTheme || !(0, local_pkg.resolveModule)(themeImport)) return styleImport;
	return [styleImport, `${themeRoot}/${importStyleTheme}`];
}
function getLegacySideEffects(path, importStyle, importStyleTheme = "default") {
	const styleImport = `${path}/style/themes/${importStyle === "css" ? `${importStyleTheme}_css` : importStyleTheme}`;
	if (!(0, local_pkg.resolveModule)(styleImport)) return;
	return styleImport;
}

//#endregion
//#region src/core/resolvers/inkline.ts
/**
* Resolver for Inkline
*
* <AUTHOR>
* @link https://github.com/inkline/inkline
*/
function InklineResolver() {
	return {
		type: "component",
		resolve: (name) => {
			if (name.match(/^I[A-Z]/)) return {
				name,
				from: "@inkline/inkline"
			};
		}
	};
}

//#endregion
//#region src/core/resolvers/ionic.ts
/**
* source: https://github.com/nuxt-modules/ionic/blob/main/src/imports.ts
* <AUTHOR>
*/
const IonicBuiltInComponents = [
	"IonAccordion",
	"IonAccordionGroup",
	"IonActionSheet",
	"IonAlert",
	"IonApp",
	"IonAvatar",
	"IonBackButton",
	"IonBackdrop",
	"IonBadge",
	"IonBreadcrumb",
	"IonBreadcrumbs",
	"IonButton",
	"IonButtons",
	"IonCard",
	"IonCardContent",
	"IonCardHeader",
	"IonCardSubtitle",
	"IonCardTitle",
	"IonCheckbox",
	"IonChip",
	"IonCol",
	"IonContent",
	"IonDatetime",
	"IonDatetimeButton",
	"IonFab",
	"IonFabButton",
	"IonFabList",
	"IonFooter",
	"IonGrid",
	"IonHeader",
	"IonIcon",
	"IonImg",
	"IonInfiniteScroll",
	"IonInfiniteScrollContent",
	"IonInput",
	"IonInputPasswordToggle",
	"IonItem",
	"IonItemDivider",
	"IonItemGroup",
	"IonItemOption",
	"IonItemOptions",
	"IonItemSliding",
	"IonLabel",
	"IonList",
	"IonListHeader",
	"IonLoading",
	"IonMenu",
	"IonMenuButton",
	"IonMenuToggle",
	"IonModal",
	"IonNav",
	"IonNavLink",
	"IonNote",
	"IonPage",
	"IonPicker",
	"IonPickerColumn",
	"IonPickerColumnOption",
	"IonPickerLegacy",
	"IonPopover",
	"IonProgressBar",
	"IonRadio",
	"IonRadioGroup",
	"IonRange",
	"IonRefresher",
	"IonRefresherContent",
	"IonReorder",
	"IonReorderGroup",
	"IonRippleEffect",
	"IonRouterOutlet",
	"IonRow",
	"IonSearchbar",
	"IonSegment",
	"IonSegmentButton",
	"IonSegmentContent",
	"IonSegmentView",
	"IonSelect",
	"IonSelectModal",
	"IonSelectOption",
	"IonSkeletonText",
	"IonSpinner",
	"IonSplitPane",
	"IonTab",
	"IonTabBar",
	"IonTabButton",
	"IonTabs",
	"IonText",
	"IonTextarea",
	"IonThumbnail",
	"IonTitle",
	"IonToast",
	"IonToggle",
	"IonToolbar"
];
/**
* Resolver for ionic framework
*
* <AUTHOR> @reslear
* @link https://ionicframework.com/
*/
function IonicResolver() {
	return {
		type: "component",
		resolve: (name) => {
			if (IonicBuiltInComponents.includes(name)) return {
				name,
				from: "@ionic/vue"
			};
		}
	};
}

//#endregion
//#region src/core/resolvers/layui-vue.ts
const matchComponents$1 = [
	{
		pattern: /^LayAvatarList$/,
		styleDir: "avatar"
	},
	{
		pattern: /^LayBreadcrumbItem$/,
		styleDir: "breadcrumb"
	},
	{
		pattern: /^(LayCarouselItem)$/,
		styleDir: "carousel"
	},
	{
		pattern: /^(LayCheckboxGroup)$/,
		styleDir: "checkbox"
	},
	{
		pattern: /^LayCol$/,
		styleDir: "row"
	},
	{
		pattern: /^(LayCollapseItem)$/,
		styleDir: "collapse"
	},
	{
		pattern: /^LayConfigProvider$/,
		styleDir: void 0
	},
	{
		pattern: /^LayCountUp$/,
		styleDir: void 0
	},
	{
		pattern: /^(LayDropdownMenu|LayDropdownMenuItem|LayDropdownSubMenu)$/,
		styleDir: "dropdown"
	},
	{
		pattern: /^(LayFormItem)$/,
		styleDir: "form"
	},
	{
		pattern: /^(LayMenuItem|LaySubMenu)$/,
		styleDir: "menu"
	},
	{
		pattern: /^(LayRadioGroup)$/,
		styleDir: "radio"
	},
	{
		pattern: /^LaySelectOption$/,
		styleDir: "select"
	},
	{
		pattern: /^LaySkeletonItem$/,
		styleDir: "skeleton"
	},
	{
		pattern: /^LaySplitPanelItem$/,
		styleDir: "splitPanel"
	},
	{
		pattern: /^LayStepItem$/,
		styleDir: "step"
	},
	{
		pattern: /^(LayTabItem)$/,
		styleDir: "tab"
	},
	{
		pattern: /^LayTimelineItem$/,
		styleDir: "timeline"
	}
];
const layuiRE = /^Lay[A-Z]/;
const layerRE = /^(layer|LayLayer)$/;
const iconsRE = /^[A-Z]\w+Icon$/;
let libName = "@layui/layui-vue";
function lowerCamelCase(str) {
	return str.charAt(0).toLowerCase() + str.slice(1);
}
function getSideEffects$3(importName, options) {
	const { importStyle = "css" } = options;
	if (!importStyle) return void 0;
	if (libName !== "@layui/layui-vue") return `${libName}/lib/index.css`;
	let styleDir = lowerCamelCase(importName.slice(3));
	for (const item of matchComponents$1) if (item.pattern.test(importName)) {
		styleDir = item.styleDir;
		break;
	}
	if (importStyle === "css" || importStyle) return styleDir ? [`@layui/layui-vue/es/${styleDir}/index.css`, "@layui/layui-vue/es/index/index.css"] : void 0;
}
function resolveComponent(importName, options) {
	let name;
	if (require_utils.isExclude(importName, options.exclude)) return void 0;
	if (options.resolveIcons && importName.match(iconsRE)) {
		name = importName;
		libName = "@layui/icons-vue";
	} else if (importName.match(layerRE)) {
		name = importName;
		libName = "@layui/layer-vue";
	} else if (importName.match(layuiRE) && !importName.match(iconsRE)) {
		name = importName;
		libName = "@layui/layui-vue";
	}
	return name ? {
		name,
		from: libName,
		sideEffects: getSideEffects$3(name, options)
	} : void 0;
}
/**
* Resolver for layui-vue
*
* @link http://www.layui-vue.com/ for layui-vue
*
*/
function LayuiVueResolver(options = {}) {
	return {
		type: "component",
		resolve: (name) => {
			return resolveComponent(name, options);
		}
	};
}

//#endregion
//#region src/core/resolvers/naive-ui.ts
/**
* Resolver for Naive UI
*
* <AUTHOR>
* @link https://www.naiveui.com/
*/
function NaiveUiResolver() {
	return {
		type: "component",
		resolve: (name) => {
			if (name.match(/^(N[A-Z]|n-[a-z])/)) return {
				name,
				from: "naive-ui"
			};
		}
	};
}

//#endregion
//#region src/core/resolvers/prime-vue.ts
const components$2 = [
	"Accordion",
	"AccordionTab",
	"AutoComplete",
	"Avatar",
	"AvatarGroup",
	"Badge",
	"BlockUI",
	"Breadcrumb",
	"Button",
	"Calendar",
	"Card",
	"Carousel",
	"CascadeSelect",
	"Chart",
	"Checkbox",
	"Chip",
	"Chips",
	"ColorPicker",
	"Column",
	"ColumnGroup",
	"ContextMenu",
	"DataTable",
	"DataView",
	"DataViewLayoutOptions",
	"DeferredContent",
	"Dialog",
	"Divider",
	"Dock",
	"Dropdown",
	"Editor",
	"Fieldset",
	"FileUpload",
	"FloatLabel",
	"FullCalendar",
	"Galleria",
	"IconField",
	"IconField",
	"Image",
	"InlineMessage",
	"Inplace",
	"InputGroup",
	"InputGroupAddon",
	"InputIcon",
	"InputMask",
	"InputNumber",
	"InputOtp",
	"InputSwitch",
	"InputText",
	"Knob",
	"Listbox",
	"MegaMenu",
	"Menu",
	"Menubar",
	"Message",
	"MeterGroup",
	"MultiSelect",
	"OrderList",
	"OrganizationChart",
	"OverlayPanel",
	"Paginator",
	"Panel",
	"PanelMenu",
	"Password",
	"PickList",
	"ProgressBar",
	"ProgressSpinner",
	"RadioButton",
	"Rating",
	"Row",
	"ScrollPanel",
	"ScrollTop",
	"SelectButton",
	"Sidebar",
	"Skeleton",
	"Slider",
	"SpeedDial",
	"SplitButton",
	"Splitter",
	"SplitterPanel",
	"Stepper",
	"StepperPanel",
	"Steps",
	"TabMenu",
	"TabPanel",
	"TabView",
	"Tag",
	"Terminal",
	"TerminalService",
	"Textarea",
	"TieredMenu",
	"Timeline",
	"Timelist",
	"ToggleButton",
	"Toolbar",
	"Tree",
	"TreeSelect",
	"TreeTable",
	"TriStateCheckbox",
	"VirtualScroller"
];
/**
* Resolver for PrimeVue - If you're using a component with the same tag as an native HTML element (e.g. button) the component must be in uppercase
*
* @link https://github.com/primefaces/primevue
*/
function PrimeVueResolver(options = {}) {
	return {
		type: "component",
		resolve: (name) => {
			const sideEffects = [];
			if (options.importStyle) sideEffects.push("primevue/resources/primevue.min.css");
			if (options.importIcons) sideEffects.push("primeicons/primeicons.css");
			if (options.importTheme) sideEffects.push(`primevue/resources/themes/${options.importTheme}/theme.css`);
			if (options.prefix) {
				if (!name.startsWith(options.prefix)) return;
				name = name.substring(options.prefix.length);
			}
			if (components$2.includes(name)) return {
				from: `primevue/${name.toLowerCase()}`,
				sideEffects
			};
		}
	};
}

//#endregion
//#region src/core/resolvers/quasar.ts
/**
* Resolver for Quasar
*
* @link https://github.com/quasarframework/quasar
*/
function QuasarResolver() {
	let components$4 = [];
	return {
		type: "component",
		resolve: async (name) => {
			if (!components$4.length) {
				const quasarApiListPath = (0, local_pkg.resolveModule)("quasar/dist/transforms/api-list.json");
				if (quasarApiListPath) components$4 = JSON.parse(await node_fs.promises.readFile(quasarApiListPath, "utf-8"));
			}
			if (components$4.includes(name)) return {
				name,
				from: "quasar"
			};
		}
	};
}

//#endregion
//#region src/core/resolvers/tdesign.ts
function TDesignResolver(options = {}) {
	const pluginList = [
		"DialogPlugin",
		"LoadingPlugin",
		"MessagePlugin",
		"NotifyPlugin"
	];
	return {
		type: "component",
		resolve: (name) => {
			const { library = "vue", exclude } = options;
			const importFrom = options.esm ? "/esm" : "";
			if (require_utils.isExclude(name, exclude)) return;
			if (options.resolveIcons && name.match(/[a-z]Icon$/)) return {
				name,
				from: `tdesign-icons-${library}${importFrom}`
			};
			if (name.startsWith("TTypography") || name.startsWith("Typography")) return {
				name: name.slice(name.startsWith("TTypography") ? 11 : 10),
				from: `tdesign-${library}${importFrom}`
			};
			if (name.match(/^T[A-Z]/) || pluginList.includes(name)) {
				const importName = name.match(/^T[A-Z]/) ? name.slice(1) : name;
				return {
					name: importName,
					from: `tdesign-${library}${importFrom}`
				};
			}
		}
	};
}

//#endregion
//#region src/core/resolvers/vant.ts
const moduleType = require_utils.isSSR ? "lib" : "es";
function getSideEffects$2(dirName, options) {
	const { importStyle = true } = options;
	if (!importStyle || require_utils.isSSR) return;
	if (importStyle === "less") return `vant/${moduleType}/${dirName}/style/less`;
	if (importStyle === "css") return `vant/${moduleType}/${dirName}/style/index`;
	return `vant/${moduleType}/${dirName}/style/index`;
}
/**
* Resolver for Vant
*
* @link https://github.com/youzan/vant
*/
function VantResolver(options = {}) {
	return {
		type: "component",
		resolve: (name) => {
			if (name.startsWith("Van")) {
				const partialName = name.slice(3);
				return {
					name: partialName,
					from: `vant/${moduleType}`,
					sideEffects: getSideEffects$2(require_utils.kebabCase(partialName), options)
				};
			}
		}
	};
}

//#endregion
//#region src/core/resolvers/varlet-ui.ts
const varFunctions = [
	"ImagePreview",
	"Snackbar",
	"Picker",
	"ActionSheet",
	"Dialog",
	"Locale",
	"StyleProvider",
	"LoadingBar"
];
const varDirectives = [
	"Ripple",
	"Lazy",
	"Hover"
];
function getResolved(name, options) {
	const { importStyle = "css", importCss = true, importLess, styleExtname = ".mjs", autoImport = false, version = "vue3" } = options;
	const path = version === "vue2" ? "@varlet-vue2/ui" : "@varlet/ui";
	const sideEffects = [];
	if (importStyle || importCss) if (importStyle === "less" || importLess) sideEffects.push(`${path}/es/${require_utils.kebabCase(name)}/style/less`);
	else sideEffects.push(`${path}/es/${require_utils.kebabCase(name)}/style/index${styleExtname}`);
	return {
		from: path,
		name: autoImport ? name : `_${name}Component`,
		sideEffects
	};
}
/**
* Resolver for VarletUI
*
* @link https://github.com/varletjs/varlet
* @link https://github.com/varletjs/varlet-vue2
*/
function VarletUIResolver(options = {}) {
	return [{
		type: "component",
		resolve: (name) => {
			const { autoImport = false } = options;
			if (autoImport && varFunctions.includes(name)) return getResolved(name, options);
			if (name.startsWith("Var")) return getResolved(name.slice(3), options);
		}
	}, {
		type: "directive",
		resolve: (name) => {
			const { directives: directives$1 = true } = options;
			if (!directives$1) return;
			if (!varDirectives.includes(name)) return;
			return getResolved(name, options);
		}
	}];
}

//#endregion
//#region src/core/resolvers/veui.ts
const VEUI_PACKAGE_NAME = "veui";
let components$1;
/**
* Resolver for VEUI
*
* @link https://github.com/ecomfe/veui
*/
function VeuiResolver(options = {}) {
	const { alias = VEUI_PACKAGE_NAME } = options;
	if (!components$1) try {
		const componentsData = require(`${alias}/components.json`);
		components$1 = new Set(componentsData.map(({ name }) => name));
	} catch {
		throw new Error("[unplugin-vue-components:veui] VEUI is not installed");
	}
	return {
		type: "component",
		resolve: (name) => {
			if (name.match(/^Veui[A-Z]/)) {
				const componentName = name.slice(4);
				if (!components$1.has(componentName)) return;
				const sideEffects = getSideEffects$1(componentName, options);
				return {
					name: componentName,
					from: alias,
					sideEffects
				};
			}
		}
	};
}
const formatters = {
	"kebab-case": require_utils.kebabCase,
	"camelCase": require_utils.camelCase,
	"PascalCase": require_utils.pascalCase
};
const peerPaths = new Map();
function assertPeerPath(peerPath) {
	if (!peerPaths.has(peerPath)) try {
		(0, mlly.resolvePathSync)(peerPath);
		peerPaths.set(peerPath, true);
	} catch {
		peerPaths.set(peerPath, false);
	}
	return peerPaths.get(peerPath);
}
function getSideEffects$1(name, { alias = VEUI_PACKAGE_NAME, modules = [], locale = "zh-Hans", global = [] }) {
	const localeModules = (locale ? Array.isArray(locale) ? locale : [locale] : []).map((locale$1) => `${alias}/locale/${locale$1}/${name}.js`);
	const peerModules = modules.map(({ package: pack, path = "components", fileName = "{module}.css", transform = "kebab-case" }) => {
		const peer = transform ? formatters[transform](name) : name;
		const file = fileName.replace(/\$?\{module\}/g, peer);
		return (0, node_path.normalize)((0, node_path.join)(pack, path, file));
	});
	return [
		...localeModules,
		...global,
		...peerModules
	].filter(assertPeerPath);
}

//#endregion
//#region src/core/resolvers/view-ui.ts
function getSideEffects(componentName) {
	const sideEffects = ["view-design/dist/styles/iview.css", "popper.js/dist/umd/popper.js"];
	if (/^Table|^Slider|^Tab/.test(componentName)) sideEffects.push("element-resize-detector");
	if (componentName.startsWith("Date")) sideEffects.push("js-calendar");
	return sideEffects;
}
const matchComponents = [{
	pattern: /^List/,
	compDir: "list"
}];
function getCompDir(compName) {
	let compPath;
	const total = matchComponents.length;
	for (let i = 0; i < total; i++) {
		const matcher = matchComponents[i];
		if (compName.match(matcher.pattern)) {
			compPath = `${matcher.compDir}/${require_utils.kebabCase(compName)}.vue`;
			break;
		}
	}
	if (!compPath) compPath = require_utils.kebabCase(compName);
	return compPath;
}
/**
* Resolver for View UI
* @requires @originjs/vite-plugin-commonjs
* <AUTHOR>
* @link https://www.iviewui.com/
* @description has known problems list below
* - select component render error PR: https://github.com/view-design/ViewUI/pull/944,  choose can't display value,because click option trigger twice,at second time,select value turn into undefined.
* - scroll component has a template syntax called lang='html',it is require html-loader,but vite plugin not support yet,remove it can run. relate pr: https://github.com/view-design/ViewUI/pull/985
*/
function ViewUiResolver() {
	return {
		type: "component",
		resolve: (name) => {
			if (name.match(/^I[A-Z]/)) {
				const compName = name.slice(1);
				return {
					from: `view-design/src/components/${getCompDir(compName)}`,
					sideEffects: getSideEffects(compName)
				};
			}
		}
	};
}

//#endregion
//#region src/core/resolvers/vuetify.ts
/**
* Resolver for Vuetify
*
* @link https://github.com/vuetifyjs/vuetify
*/
function VuetifyResolver() {
	return {
		type: "component",
		resolve: (name) => {
			if (name.match(/^V[A-Z]/)) return {
				name,
				from: "vuetify/lib"
			};
		}
	};
}
/**
* Resolver for Vuetify 3 Beta
*
* @link https://github.com/vuetifyjs/vuetify
*/
function Vuetify3Resolver() {
	return {
		type: "component",
		resolve: (name) => {
			if (name.match(/^V[A-Z]/)) return {
				name,
				from: "vuetify/components"
			};
		}
	};
}

//#endregion
//#region src/core/resolvers/vueuse.ts
let components;
/**
* Resolver for VueUse
*
* @link https://github.com/vueuse/vueuse
*/
function VueUseComponentsResolver() {
	return {
		type: "component",
		resolve: (name) => {
			if (!components) {
				let indexesJson;
				try {
					const corePath = (0, local_pkg.resolveModule)("@vueuse/core") || node_process.default.cwd();
					const path = (0, local_pkg.resolveModule)("@vueuse/core/indexes.json") || (0, local_pkg.resolveModule)("@vueuse/metadata/index.json") || (0, local_pkg.resolveModule)("@vueuse/metadata/index.json", { paths: [corePath] });
					indexesJson = JSON.parse((0, node_fs.readFileSync)(path, "utf-8"));
					components = indexesJson.functions.filter((i) => i.component && i.name).map(({ name: name$1 }) => name$1[0].toUpperCase() + name$1.slice(1));
				} catch (error) {
					console.error(error);
					throw new Error("[vue-components] failed to load @vueuse/core, have you installed it?");
				}
			}
			if (components && components.includes(name)) return {
				name,
				as: name,
				from: "@vueuse/components"
			};
		}
	};
}

//#endregion
//#region src/core/resolvers/vueuse-directive.ts
let directives;
/**
* Resolver for VueUse
*
* @link https://github.com/vueuse/vueuse
*/
function VueUseDirectiveResolver() {
	return {
		type: "directive",
		resolve: (name) => {
			if (!directives) {
				let indexesJson;
				try {
					const corePath = (0, local_pkg.resolveModule)("@vueuse/core") || node_process.default.cwd();
					const path = (0, local_pkg.resolveModule)("@vueuse/core/indexes.json") || (0, local_pkg.resolveModule)("@vueuse/metadata/index.json") || (0, local_pkg.resolveModule)("@vueuse/metadata/index.json", { paths: [corePath] });
					indexesJson = JSON.parse((0, node_fs.readFileSync)(path, "utf-8"));
					directives = indexesJson.functions.filter((i) => i.directive && i.name).map(({ name: name$1 }) => name$1[0].toUpperCase() + name$1.slice(1)).map((name$1) => name$1.startsWith("Use") ? name$1.slice(3) : name$1);
				} catch (error) {
					console.error(error);
					throw new Error("[vue-components] failed to load @vueuse/core, have you installed it?");
				}
			}
			if (directives && directives.includes(name)) return {
				name: `v${name}`,
				as: name,
				from: "@vueuse/components"
			};
		}
	};
}

//#endregion
exports.AntDesignVueResolver = AntDesignVueResolver;
exports.ArcoResolver = ArcoResolver;
exports.BootstrapVue3Resolver = BootstrapVue3Resolver;
exports.BootstrapVueNextResolver = BootstrapVueNextResolver;
exports.BootstrapVueResolver = BootstrapVueResolver;
exports.DevUiResolver = DevUiResolver;
exports.ElementPlusResolver = ElementPlusResolver;
exports.ElementUiResolver = ElementUiResolver;
exports.HeadlessUiResolver = HeadlessUiResolver;
exports.IduxResolver = IduxResolver;
exports.InklineResolver = InklineResolver;
exports.IonicBuiltInComponents = IonicBuiltInComponents;
exports.IonicResolver = IonicResolver;
exports.LayuiVueResolver = LayuiVueResolver;
exports.NaiveUiResolver = NaiveUiResolver;
exports.PrimeVueResolver = PrimeVueResolver;
exports.QuasarResolver = QuasarResolver;
exports.TDesignResolver = TDesignResolver;
exports.VantResolver = VantResolver;
exports.VarletUIResolver = VarletUIResolver;
exports.VeuiResolver = VeuiResolver;
exports.ViewUiResolver = ViewUiResolver;
exports.VueUseComponentsResolver = VueUseComponentsResolver;
exports.VueUseDirectiveResolver = VueUseDirectiveResolver;
exports.Vuetify3Resolver = Vuetify3Resolver;
exports.VuetifyResolver = VuetifyResolver;
exports.getResolved = getResolved;