/**
 * Mapbox地图管理 Composable
 * 提供地图实例管理和基础功能
 */

import { ref, onUnmounted } from 'vue'
import mapboxgl from 'mapbox-gl'
import { mapboxConfig, validateConfig, getTerrainSourceConfig, getTerrainLayerConfig } from '@/config/mapbox'
import { createTerrainVarianceProcessor, type TerrainVarianceProcessor } from '@/utils/terrainVariance'
import type { ProcessedTrackData } from '@/types/track'

export function useMapbox() {
  const mapInstance = ref<mapboxgl.Map | null>(null)
  const isMapLoaded = ref(false)
  const mapError = ref<string | null>(null)

  /**
   * 初始化地图
   */
  async function initializeMap(containerId: string, options?: Partial<mapboxgl.MapboxOptions>) {
    try {
      // 验证配置
      if (!validateConfig()) {
        throw new Error('Mapbox配置验证失败，请检查环境变量')
      }

      // 设置访问令牌
      mapboxgl.accessToken = mapboxConfig.accessToken

      // 默认配置
      const defaultOptions: mapboxgl.MapboxOptions = {
        container: containerId,
        style: mapboxConfig.style,
        center: [24.9384, 60.1699], // 默认中心点 (赫尔辛基)
        zoom: 10,
        pitch: 45,
        bearing: 0,
        antialias: true,
        ...options
      }

      // 创建地图实例
      const map = new mapboxgl.Map(defaultOptions)
      mapInstance.value = map

      // 等待地图加载完成
      await new Promise<void>((resolve, reject) => {
        map.on('load', () => {
          isMapLoaded.value = true
          resolve()
        })

        map.on('error', (e) => {
          mapError.value = e.error.message
          reject(e.error)
        })
      })

      return map
    } catch (error) {
      mapError.value = error instanceof Error ? error.message : '地图初始化失败'
      throw error
    }
  }

  /**
   * 设置地形
   */
  function setupTerrain() {
    const map = mapInstance.value
    if (!map || !isMapLoaded.value) return

    try {
      // 添加地形数据源
      map.addSource('mapbox-dem', getTerrainSourceConfig() as mapboxgl.RasterDEMSourceSpecification)
      
      // 设置地形
      map.setTerrain(getTerrainLayerConfig() as mapboxgl.TerrainSpecification)
    } catch (error) {
      console.error('地形设置失败:', error)
    }
  }

  /**
   * 添加轨迹到地图
   */
  function addTrackToMap(trackData: ProcessedTrackData) {
    const map = mapInstance.value
    if (!map || !isMapLoaded.value) return

    try {
      // 添加轨迹数据源
      map.addSource('track-data', {
        type: 'geojson',
        data: {
          type: 'LineString',
          coordinates: trackData.points.map(point => [point.lng, point.lat, point.elevation || 0])
        }
      })

      // 添加轨迹线图层
      map.addLayer({
        id: 'track-line',
        type: 'line',
        source: 'track-data',
        layout: {
          'line-join': 'round',
          'line-cap': 'round'
        },
        paint: {
          'line-color': '#ff6b35',
          'line-width': 4,
          'line-opacity': 0.8
        }
      })

      // 添加起点标记
      const startPoint = trackData.points[0]
      if (startPoint) {
        map.addSource('start-point', {
          type: 'geojson',
          data: {
            type: 'Point',
            coordinates: [startPoint.lng, startPoint.lat]
          }
        })

        map.addLayer({
          id: 'start-marker',
          type: 'circle',
          source: 'start-point',
          paint: {
            'circle-radius': 8,
            'circle-color': '#00ff00',
            'circle-stroke-color': '#ffffff',
            'circle-stroke-width': 2
          }
        })
      }

      // 添加终点标记
      const endPoint = trackData.points[trackData.points.length - 1]
      if (endPoint) {
        map.addSource('end-point', {
          type: 'geojson',
          data: {
            type: 'Point',
            coordinates: [endPoint.lng, endPoint.lat]
          }
        })

        map.addLayer({
          id: 'end-marker',
          type: 'circle',
          source: 'end-point',
          paint: {
            'circle-radius': 8,
            'circle-color': '#ff0000',
            'circle-stroke-color': '#ffffff',
            'circle-stroke-width': 2
          }
        })
      }

      // 调整视图到轨迹范围
      fitToTrack(trackData)

      console.log(`轨迹已添加到地图，共 ${trackData.points.length} 个点`)
    } catch (error) {
      console.error('添加轨迹失败:', error)
    }
  }

  /**
   * 调整视图到轨迹范围
   */
  function fitToTrack(trackData: ProcessedTrackData) {
    const map = mapInstance.value
    if (!map || !trackData.points.length) return

    const bounds = new mapboxgl.LngLatBounds()
    trackData.points.forEach(point => {
      bounds.extend([point.lng, point.lat])
    })
    
    map.fitBounds(bounds, {
      padding: 50,
      duration: 2000
    })
  }

  /**
   * 清理地图实例
   */
  function cleanup() {
    if (mapInstance.value) {
      mapInstance.value.remove()
      mapInstance.value = null
      isMapLoaded.value = false
    }
  }

  // 组件卸载时自动清理
  onUnmounted(() => {
    cleanup()
  })

  return {
    // 状态
    mapInstance,
    isMapLoaded,
    mapError,
    
    // 方法
    initializeMap,
    setupTerrain,
    addTrackToMap,
    fitToTrack,
    cleanup
  }
}
