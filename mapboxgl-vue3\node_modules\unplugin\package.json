{"name": "unplugin", "type": "module", "version": "2.3.5", "packageManager": "pnpm@10.11.0", "description": "Unified plugin system for build tools", "license": "MIT", "homepage": "https://unplugin.unjs.io", "repository": {"type": "git", "url": "git+https://github.com/unjs/unplugin.git"}, "sideEffects": false, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "./dist/webpack/loaders/*": "./dist/webpack/loaders/*.cjs", "./dist/rspack/loaders/*": "./dist/rspack/loaders/*.cjs"}, "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "engines": {"node": ">=18.12.0"}, "scripts": {"build": "tsdown", "dev": "tsdown --watch src", "lint": "eslint --cache .", "lint:fix": "nr lint --fix", "typecheck": "tsc --noEmit", "docs:dev": "pnpm -C docs run dev", "docs:build": "pnpm -C docs run build", "docs:gen-files": "pnpm -C docs run gen-files", "prepublishOnly": "nr build", "release": "bumpp --all && npm publish", "test": "nr test:build && vitest run --pool=forks", "test:build": "jiti scripts/buildFixtures.ts"}, "dependencies": {"acorn": "^8.14.1", "picomatch": "^4.0.2", "webpack-virtual-modules": "^0.6.2"}, "devDependencies": {"@ampproject/remapping": "^2.3.0", "@antfu/eslint-config": "^4.13.2", "@antfu/ni": "^25.0.0", "@farmfe/cli": "^1.0.4", "@farmfe/core": "^1.7.5", "@rspack/cli": "^1.3.12", "@rspack/core": "^1.3.12", "@types/fs-extra": "^11.0.4", "@types/node": "^22.15.21", "@types/picomatch": "^4.0.0", "ansis": "^4.0.0", "bumpp": "^10.1.1", "esbuild": "^0.25.5", "esbuild-plugin-copy": "^2.1.1", "eslint": "^9.27.0", "eslint-plugin-format": "^1.0.1", "fast-glob": "^3.3.3", "fs-extra": "^11.3.0", "jiti": "^2.4.2", "lint-staged": "^16.0.0", "magic-string": "^0.30.17", "rolldown": "^1.0.0-beta.9", "rollup": "^4.41.1", "simple-git-hooks": "^2.13.0", "tsdown": "^0.12.3", "typescript": "~5.8.3", "unloader": "^0.4.5", "unplugin": "workspace:*", "unplugin-unused": "^0.5.0", "vite": "^6.3.5", "vitest": "^3.1.4", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}, "resolutions": {"esbuild": "^0.25.5"}, "simple-git-hooks": {"pre-commit": "pnpm i --frozen-lockfile --ignore-scripts --offline && npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}}