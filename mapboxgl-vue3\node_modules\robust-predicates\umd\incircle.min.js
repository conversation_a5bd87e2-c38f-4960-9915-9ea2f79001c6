!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t=t||self).predicates={})}(this,function(t){"use strict";const n=134217729,e=33306690738754706e-32;function o(t,n,e,o,f){let r,c,i,s,u=n[0],a=o[0],l=0,d=0;a>u==a>-u?(r=u,u=n[++l]):(r=a,a=o[++d]);let b=0;if(l<t&&d<e)for(a>u==a>-u?(i=r-((c=u+r)-u),u=n[++l]):(i=r-((c=a+r)-a),a=o[++d]),r=c,0!==i&&(f[b++]=i);l<t&&d<e;)a>u==a>-u?(i=r-((c=r+u)-(s=c-r))+(u-s),u=n[++l]):(i=r-((c=r+a)-(s=c-r))+(a-s),a=o[++d]),r=c,0!==i&&(f[b++]=i);for(;l<t;)i=r-((c=r+u)-(s=c-r))+(u-s),u=n[++l],r=c,0!==i&&(f[b++]=i);for(;d<e;)i=r-((c=r+a)-(s=c-r))+(a-s),a=o[++d],r=c,0!==i&&(f[b++]=i);return 0===r&&0!==b||(f[b++]=r),b}function f(t,n,e,f,r,c,i,s){return o(o(t,n,e,f,i),i,r,c,s)}function r(t,e,o,f){let r,c,i,s,u,a,l,d,b,h,p;p=o-(h=(l=n*o)-(l-o));let M=e[0],y=0;0!==(i=(b=M-(d=(l=n*M)-(l-M)))*p-((r=M*o)-d*h-b*h-d*p))&&(f[y++]=i);for(let x=1;x<t;x++)0!==(i=r-((c=r+(u=(b=(M=e[x])-(d=(l=n*M)-(l-M)))*p-((s=M*o)-d*h-b*h-d*p)))-(a=c-r))+(u-a))&&(f[y++]=i),0!==(i=c-((r=s+c)-s))&&(f[y++]=i);return 0===r&&0!==y||(f[y++]=r),y}function c(t){return new Float64Array(t)}const i=11102230246251577e-31,s=4440892098500632e-31,u=5423418723394464e-46,a=c(4),l=c(4),d=c(4),b=c(4),h=c(4),p=c(4),M=c(4),y=c(4),x=c(8),j=c(8),m=c(8),_=c(8),v=c(8),w=c(8),A=c(8),F=c(8),O=c(8),P=c(4),g=c(4),k=c(4),q=c(8),z=c(16),B=c(16),C=c(16),D=c(32),E=c(32),G=c(48),H=c(64);let I=c(1152),J=c(1152);function K(t,n,e){t=o(t,I,n,e,J);const f=I;return I=J,J=f,t}t.incircle=function(t,c,J,L,N,Q,R,S){const T=t-R,U=J-R,V=N-R,W=c-S,X=L-S,Y=Q-S,Z=U*Y,$=V*X,tt=T*T+W*W,nt=V*W,et=T*Y,ot=U*U+X*X,ft=T*X,rt=U*W,ct=V*V+Y*Y,it=tt*(Z-$)+ot*(nt-et)+ct*(ft-rt),st=(Math.abs(Z)+Math.abs($))*tt+(Math.abs(nt)+Math.abs(et))*ot+(Math.abs(ft)+Math.abs(rt))*ct,ut=i*st;return it>ut||-it>ut?it:function(t,c,i,J,L,N,Q,R,S){let T,U,V,W,X,Y,Z,$,tt,nt,et,ot,ft,rt,ct,it,st,ut,at,lt,dt,bt,ht,pt,Mt,yt,xt,jt,mt,_t,vt,wt,At,Ft,Ot;const Pt=t-Q,gt=i-Q,kt=L-Q,qt=c-R,zt=J-R,Bt=N-R;bt=(wt=(Mt=gt-(pt=(ht=n*gt)-(ht-gt)))*(xt=Bt-(yt=(ht=n*Bt)-(ht-Bt)))-((vt=gt*Bt)-pt*yt-Mt*yt-pt*xt))-(jt=wt-(Ft=(Mt=kt-(pt=(ht=n*kt)-(ht-kt)))*(xt=zt-(yt=(ht=n*zt)-(ht-zt)))-((At=kt*zt)-pt*yt-Mt*yt-pt*xt))),a[0]=wt-(jt+bt)+(bt-Ft),bt=(_t=vt-((mt=vt+jt)-(bt=mt-vt))+(jt-bt))-(jt=_t-At),a[1]=_t-(jt+bt)+(bt-At),bt=(Ot=mt+jt)-mt,a[2]=mt-(Ot-bt)+(jt-bt),a[3]=Ot,bt=(wt=(Mt=kt-(pt=(ht=n*kt)-(ht-kt)))*(xt=qt-(yt=(ht=n*qt)-(ht-qt)))-((vt=kt*qt)-pt*yt-Mt*yt-pt*xt))-(jt=wt-(Ft=(Mt=Pt-(pt=(ht=n*Pt)-(ht-Pt)))*(xt=Bt-(yt=(ht=n*Bt)-(ht-Bt)))-((At=Pt*Bt)-pt*yt-Mt*yt-pt*xt))),l[0]=wt-(jt+bt)+(bt-Ft),bt=(_t=vt-((mt=vt+jt)-(bt=mt-vt))+(jt-bt))-(jt=_t-At),l[1]=_t-(jt+bt)+(bt-At),bt=(Ot=mt+jt)-mt,l[2]=mt-(Ot-bt)+(jt-bt),l[3]=Ot,bt=(wt=(Mt=Pt-(pt=(ht=n*Pt)-(ht-Pt)))*(xt=zt-(yt=(ht=n*zt)-(ht-zt)))-((vt=Pt*zt)-pt*yt-Mt*yt-pt*xt))-(jt=wt-(Ft=(Mt=gt-(pt=(ht=n*gt)-(ht-gt)))*(xt=qt-(yt=(ht=n*qt)-(ht-qt)))-((At=gt*qt)-pt*yt-Mt*yt-pt*xt))),d[0]=wt-(jt+bt)+(bt-Ft),bt=(_t=vt-((mt=vt+jt)-(bt=mt-vt))+(jt-bt))-(jt=_t-At),d[1]=_t-(jt+bt)+(bt-At),bt=(Ot=mt+jt)-mt,d[2]=mt-(Ot-bt)+(jt-bt),d[3]=Ot;let Ct=function(t,n){let e=n[0];for(let o=1;o<t;o++)e+=n[o];return e}(T=o(o(o(r(r(4,a,Pt,q),q,Pt,z),z,r(r(4,a,qt,q),q,qt,B),B,D),D,o(r(r(4,l,gt,q),q,gt,z),z,r(r(4,l,zt,q),q,zt,B),B,E),E,H),H,o(r(r(4,d,kt,q),q,kt,z),z,r(r(4,d,Bt,q),q,Bt,B),B,D),D,I),I),Dt=s*S;if(Ct>=Dt||-Ct>=Dt)return Ct;if(U=t-(Pt+(bt=t-Pt))+(bt-Q),X=c-(qt+(bt=c-qt))+(bt-R),V=i-(gt+(bt=i-gt))+(bt-Q),Y=J-(zt+(bt=J-zt))+(bt-R),W=L-(kt+(bt=L-kt))+(bt-Q),Z=N-(Bt+(bt=N-Bt))+(bt-R),0===U&&0===V&&0===W&&0===X&&0===Y&&0===Z)return Ct;if(Dt=u*S+e*Math.abs(Ct),(Ct+=(Pt*Pt+qt*qt)*(gt*Z+Bt*V-(zt*W+kt*Y))+2*(Pt*U+qt*X)*(gt*Bt-zt*kt)+((gt*gt+zt*zt)*(kt*X+qt*W-(Bt*U+Pt*Z))+2*(gt*V+zt*Y)*(kt*qt-Bt*Pt))+((kt*kt+Bt*Bt)*(Pt*Y+zt*U-(qt*V+gt*X))+2*(kt*W+Bt*Z)*(Pt*zt-qt*gt)))>=Dt||-Ct>=Dt)return Ct;if(0===V&&0===Y&&0===W&&0===Z||(bt=(jt=(wt=(Mt=Pt-(pt=(ht=n*Pt)-(ht-Pt)))*Mt-((vt=Pt*Pt)-pt*pt-(pt+pt)*Mt))+(Ft=(Mt=qt-(pt=(ht=n*qt)-(ht-qt)))*Mt-((At=qt*qt)-pt*pt-(pt+pt)*Mt)))-wt,b[0]=wt-(jt-bt)+(Ft-bt),bt=(jt=(_t=vt-((mt=vt+jt)-(bt=mt-vt))+(jt-bt))+At)-_t,b[1]=_t-(jt-bt)+(At-bt),bt=(Ot=mt+jt)-mt,b[2]=mt-(Ot-bt)+(jt-bt),b[3]=Ot),0===W&&0===Z&&0===U&&0===X||(bt=(jt=(wt=(Mt=gt-(pt=(ht=n*gt)-(ht-gt)))*Mt-((vt=gt*gt)-pt*pt-(pt+pt)*Mt))+(Ft=(Mt=zt-(pt=(ht=n*zt)-(ht-zt)))*Mt-((At=zt*zt)-pt*pt-(pt+pt)*Mt)))-wt,h[0]=wt-(jt-bt)+(Ft-bt),bt=(jt=(_t=vt-((mt=vt+jt)-(bt=mt-vt))+(jt-bt))+At)-_t,h[1]=_t-(jt-bt)+(At-bt),bt=(Ot=mt+jt)-mt,h[2]=mt-(Ot-bt)+(jt-bt),h[3]=Ot),0===U&&0===X&&0===V&&0===Y||(bt=(jt=(wt=(Mt=kt-(pt=(ht=n*kt)-(ht-kt)))*Mt-((vt=kt*kt)-pt*pt-(pt+pt)*Mt))+(Ft=(Mt=Bt-(pt=(ht=n*Bt)-(ht-Bt)))*Mt-((At=Bt*Bt)-pt*pt-(pt+pt)*Mt)))-wt,p[0]=wt-(jt-bt)+(Ft-bt),bt=(jt=(_t=vt-((mt=vt+jt)-(bt=mt-vt))+(jt-bt))+At)-_t,p[1]=_t-(jt-bt)+(At-bt),bt=(Ot=mt+jt)-mt,p[2]=mt-(Ot-bt)+(jt-bt),p[3]=Ot),0!==U&&($=r(4,a,U,x),T=K(T,f(r($,x,2*Pt,z),z,r(r(4,p,U,q),q,zt,B),B,r(r(4,h,U,q),q,-Bt,C),C,D,G),G)),0!==X&&(tt=r(4,a,X,j),T=K(T,f(r(tt,j,2*qt,z),z,r(r(4,h,X,q),q,kt,B),B,r(r(4,p,X,q),q,-gt,C),C,D,G),G)),0!==V&&(nt=r(4,l,V,m),T=K(T,f(r(nt,m,2*gt,z),z,r(r(4,b,V,q),q,Bt,B),B,r(r(4,p,V,q),q,-qt,C),C,D,G),G)),0!==Y&&(et=r(4,l,Y,_),T=K(T,f(r(et,_,2*zt,z),z,r(r(4,p,Y,q),q,Pt,B),B,r(r(4,b,Y,q),q,-kt,C),C,D,G),G)),0!==W&&(ot=r(4,d,W,v),T=K(T,f(r(ot,v,2*kt,z),z,r(r(4,h,W,q),q,qt,B),B,r(r(4,b,W,q),q,-zt,C),C,D,G),G)),0!==Z&&(ft=r(4,d,Z,w),T=K(T,f(r(ft,w,2*Bt,z),z,r(r(4,b,Z,q),q,gt,B),B,r(r(4,h,Z,q),q,-Pt,C),C,D,G),G)),0!==U||0!==X){if(0!==V||0!==Y||0!==W||0!==Z?(bt=(jt=(wt=(Mt=V-(pt=(ht=n*V)-(ht-V)))*(xt=Bt-(yt=(ht=n*Bt)-(ht-Bt)))-((vt=V*Bt)-pt*yt-Mt*yt-pt*xt))+(Ft=(Mt=gt-(pt=(ht=n*gt)-(ht-gt)))*(xt=Z-(yt=(ht=n*Z)-(ht-Z)))-((At=gt*Z)-pt*yt-Mt*yt-pt*xt)))-wt,M[0]=wt-(jt-bt)+(Ft-bt),bt=(jt=(_t=vt-((mt=vt+jt)-(bt=mt-vt))+(jt-bt))+At)-_t,M[1]=_t-(jt-bt)+(At-bt),bt=(Ot=mt+jt)-mt,M[2]=mt-(Ot-bt)+(jt-bt),M[3]=Ot,bt=(jt=(wt=(Mt=W-(pt=(ht=n*W)-(ht-W)))*(xt=-zt-(yt=(ht=n*-zt)-(ht- -zt)))-((vt=W*-zt)-pt*yt-Mt*yt-pt*xt))+(Ft=(Mt=kt-(pt=(ht=n*kt)-(ht-kt)))*(xt=-Y-(yt=(ht=n*-Y)-(ht- -Y)))-((At=kt*-Y)-pt*yt-Mt*yt-pt*xt)))-wt,y[0]=wt-(jt-bt)+(Ft-bt),bt=(jt=(_t=vt-((mt=vt+jt)-(bt=mt-vt))+(jt-bt))+At)-_t,y[1]=_t-(jt-bt)+(At-bt),bt=(Ot=mt+jt)-mt,y[2]=mt-(Ot-bt)+(jt-bt),y[3]=Ot,ct=o(4,M,4,y,F),bt=(wt=(Mt=V-(pt=(ht=n*V)-(ht-V)))*(xt=Z-(yt=(ht=n*Z)-(ht-Z)))-((vt=V*Z)-pt*yt-Mt*yt-pt*xt))-(jt=wt-(Ft=(Mt=W-(pt=(ht=n*W)-(ht-W)))*(xt=Y-(yt=(ht=n*Y)-(ht-Y)))-((At=W*Y)-pt*yt-Mt*yt-pt*xt))),g[0]=wt-(jt+bt)+(bt-Ft),bt=(_t=vt-((mt=vt+jt)-(bt=mt-vt))+(jt-bt))-(jt=_t-At),g[1]=_t-(jt+bt)+(bt-At),bt=(Ot=mt+jt)-mt,g[2]=mt-(Ot-bt)+(jt-bt),g[3]=Ot,ut=4):(F[0]=0,ct=1,g[0]=0,ut=1),0!==U){const t=r(ct,F,U,C);T=K(T,o(r($,x,U,z),z,r(t,C,2*Pt,D),D,G),G);const n=r(ut,g,U,q);T=K(T,f(r(n,q,2*Pt,z),z,r(n,q,U,B),B,r(t,C,U,D),D,E,H),H),0!==Y&&(T=K(T,r(r(4,p,U,q),q,Y,z),z)),0!==Z&&(T=K(T,r(r(4,h,-U,q),q,Z,z),z))}if(0!==X){const t=r(ct,F,X,C);T=K(T,o(r(tt,j,X,z),z,r(t,C,2*qt,D),D,G),G);const n=r(ut,g,X,q);T=K(T,f(r(n,q,2*qt,z),z,r(n,q,X,B),B,r(t,C,X,D),D,E,H),H)}}if(0!==V||0!==Y){if(0!==W||0!==Z||0!==U||0!==X?(bt=(jt=(wt=(Mt=W-(pt=(ht=n*W)-(ht-W)))*(xt=qt-(yt=(ht=n*qt)-(ht-qt)))-((vt=W*qt)-pt*yt-Mt*yt-pt*xt))+(Ft=(Mt=kt-(pt=(ht=n*kt)-(ht-kt)))*(xt=X-(yt=(ht=n*X)-(ht-X)))-((At=kt*X)-pt*yt-Mt*yt-pt*xt)))-wt,M[0]=wt-(jt-bt)+(Ft-bt),bt=(jt=(_t=vt-((mt=vt+jt)-(bt=mt-vt))+(jt-bt))+At)-_t,M[1]=_t-(jt-bt)+(At-bt),bt=(Ot=mt+jt)-mt,M[2]=mt-(Ot-bt)+(jt-bt),M[3]=Ot,bt=(jt=(wt=(Mt=U-(pt=(ht=n*U)-(ht-U)))*(xt=(lt=-Bt)-(yt=(ht=n*lt)-(ht-lt)))-((vt=U*lt)-pt*yt-Mt*yt-pt*xt))+(Ft=(Mt=Pt-(pt=(ht=n*Pt)-(ht-Pt)))*(xt=(dt=-Z)-(yt=(ht=n*dt)-(ht-dt)))-((At=Pt*dt)-pt*yt-Mt*yt-pt*xt)))-wt,y[0]=wt-(jt-bt)+(Ft-bt),bt=(jt=(_t=vt-((mt=vt+jt)-(bt=mt-vt))+(jt-bt))+At)-_t,y[1]=_t-(jt-bt)+(At-bt),bt=(Ot=mt+jt)-mt,y[2]=mt-(Ot-bt)+(jt-bt),y[3]=Ot,it=o(4,M,4,y,O),bt=(wt=(Mt=W-(pt=(ht=n*W)-(ht-W)))*(xt=X-(yt=(ht=n*X)-(ht-X)))-((vt=W*X)-pt*yt-Mt*yt-pt*xt))-(jt=wt-(Ft=(Mt=U-(pt=(ht=n*U)-(ht-U)))*(xt=Z-(yt=(ht=n*Z)-(ht-Z)))-((At=U*Z)-pt*yt-Mt*yt-pt*xt))),k[0]=wt-(jt+bt)+(bt-Ft),bt=(_t=vt-((mt=vt+jt)-(bt=mt-vt))+(jt-bt))-(jt=_t-At),k[1]=_t-(jt+bt)+(bt-At),bt=(Ot=mt+jt)-mt,k[2]=mt-(Ot-bt)+(jt-bt),k[3]=Ot,at=4):(O[0]=0,it=1,k[0]=0,at=1),0!==V){const t=r(it,O,V,C);T=K(T,o(r(nt,m,V,z),z,r(t,C,2*gt,D),D,G),G);const n=r(at,k,V,q);T=K(T,f(r(n,q,2*gt,z),z,r(n,q,V,B),B,r(t,C,V,D),D,E,H),H),0!==Z&&(T=K(T,r(r(4,b,V,q),q,Z,z),z)),0!==X&&(T=K(T,r(r(4,p,-V,q),q,X,z),z))}if(0!==Y){const t=r(it,O,Y,C);T=K(T,o(r(et,_,Y,z),z,r(t,C,2*zt,D),D,G),G);const n=r(at,k,Y,q);T=K(T,f(r(n,q,2*zt,z),z,r(n,q,Y,B),B,r(t,C,Y,D),D,E,H),H)}}if(0!==W||0!==Z){if(0!==U||0!==X||0!==V||0!==Y?(bt=(jt=(wt=(Mt=U-(pt=(ht=n*U)-(ht-U)))*(xt=zt-(yt=(ht=n*zt)-(ht-zt)))-((vt=U*zt)-pt*yt-Mt*yt-pt*xt))+(Ft=(Mt=Pt-(pt=(ht=n*Pt)-(ht-Pt)))*(xt=Y-(yt=(ht=n*Y)-(ht-Y)))-((At=Pt*Y)-pt*yt-Mt*yt-pt*xt)))-wt,M[0]=wt-(jt-bt)+(Ft-bt),bt=(jt=(_t=vt-((mt=vt+jt)-(bt=mt-vt))+(jt-bt))+At)-_t,M[1]=_t-(jt-bt)+(At-bt),bt=(Ot=mt+jt)-mt,M[2]=mt-(Ot-bt)+(jt-bt),M[3]=Ot,bt=(jt=(wt=(Mt=V-(pt=(ht=n*V)-(ht-V)))*(xt=(lt=-qt)-(yt=(ht=n*lt)-(ht-lt)))-((vt=V*lt)-pt*yt-Mt*yt-pt*xt))+(Ft=(Mt=gt-(pt=(ht=n*gt)-(ht-gt)))*(xt=(dt=-X)-(yt=(ht=n*dt)-(ht-dt)))-((At=gt*dt)-pt*yt-Mt*yt-pt*xt)))-wt,y[0]=wt-(jt-bt)+(Ft-bt),bt=(jt=(_t=vt-((mt=vt+jt)-(bt=mt-vt))+(jt-bt))+At)-_t,y[1]=_t-(jt-bt)+(At-bt),bt=(Ot=mt+jt)-mt,y[2]=mt-(Ot-bt)+(jt-bt),y[3]=Ot,rt=o(4,M,4,y,A),bt=(wt=(Mt=U-(pt=(ht=n*U)-(ht-U)))*(xt=Y-(yt=(ht=n*Y)-(ht-Y)))-((vt=U*Y)-pt*yt-Mt*yt-pt*xt))-(jt=wt-(Ft=(Mt=V-(pt=(ht=n*V)-(ht-V)))*(xt=X-(yt=(ht=n*X)-(ht-X)))-((At=V*X)-pt*yt-Mt*yt-pt*xt))),P[0]=wt-(jt+bt)+(bt-Ft),bt=(_t=vt-((mt=vt+jt)-(bt=mt-vt))+(jt-bt))-(jt=_t-At),P[1]=_t-(jt+bt)+(bt-At),bt=(Ot=mt+jt)-mt,P[2]=mt-(Ot-bt)+(jt-bt),P[3]=Ot,st=4):(A[0]=0,rt=1,P[0]=0,st=1),0!==W){const t=r(rt,A,W,C);T=K(T,o(r(ot,v,W,z),z,r(t,C,2*kt,D),D,G),G);const n=r(st,P,W,q);T=K(T,f(r(n,q,2*kt,z),z,r(n,q,W,B),B,r(t,C,W,D),D,E,H),H),0!==X&&(T=K(T,r(r(4,h,W,q),q,X,z),z)),0!==Y&&(T=K(T,r(r(4,b,-W,q),q,Y,z),z))}if(0!==Z){const t=r(rt,A,Z,C);T=K(T,o(r(ft,w,Z,z),z,r(t,C,2*Bt,D),D,G),G);const n=r(st,P,Z,q);T=K(T,f(r(n,q,2*Bt,z),z,r(n,q,Z,B),B,r(t,C,Z,D),D,E,H),H)}}return I[T-1]}(t,c,J,L,N,Q,R,S,st)},t.incirclefast=function(t,n,e,o,f,r,c,i){const s=t-c,u=n-i,a=e-c,l=o-i,d=f-c,b=r-i;return(s*s+u*u)*(a*b-d*l)+(a*a+l*l)*(d*u-s*b)+(d*d+b*b)*(s*l-a*u)},Object.defineProperty(t,"__esModule",{value:!0})});
