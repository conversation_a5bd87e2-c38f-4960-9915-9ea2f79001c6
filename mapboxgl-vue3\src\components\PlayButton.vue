<template>
  <button 
    class="play-button" 
    @click="togglePlay"
    :class="{ 'playing': isPlaying }"
  >
    <div class="play-icon" v-if="!isPlaying">
      <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
        <path 
          d="M15 10L30 20L15 30V10Z" 
          fill="currentColor"
        />
      </svg>
    </div>
    <div class="pause-icon" v-else>
      <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
        <rect x="12" y="10" width="6" height="20" fill="currentColor"/>
        <rect x="22" y="10" width="6" height="20" fill="currentColor"/>
      </svg>
    </div>
  </button>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const isPlaying = ref(false)

const togglePlay = () => {
  isPlaying.value = !isPlaying.value
  console.log('Play state:', isPlaying.value ? 'Playing' : 'Paused')
}
</script>

<style scoped>
.play-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #007bff;
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.play-button:hover {
  background-color: #0056b3;
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
}

.play-button:active {
  transform: scale(0.95);
}

.play-button.playing {
  background-color: #dc3545;
}

.play-button.playing:hover {
  background-color: #c82333;
}

.play-icon,
.pause-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-icon svg {
  margin-left: 2px; /* 微调播放图标位置 */
}
</style>
