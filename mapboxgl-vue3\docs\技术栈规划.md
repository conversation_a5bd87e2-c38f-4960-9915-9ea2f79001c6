# MapboxGL Vue3 技术栈规划

## 核心技术栈

### 前端框架
- **Vue 3.4+** - 组合式API，响应式系统
- **Vite 5+** - 快速构建工具，HMR支持
- **TypeScript 5+** - 类型安全，开发体验

### 状态管理
- **Pinia 2+** - Vue3官方推荐状态管理
- **VueUse** - Vue组合式工具库

### 地图渲染
- **Mapbox GL JS 3+** - 高性能地图引擎
- **WebGL API** - 自定义图层渲染
- **gl-matrix** - WebGL数学计算库

### 数据处理
- **模拟API** - 本地JSON文件模拟后端响应
- **坐标数据集** - 本地GeoJSON格式数据
- **Axios** - HTTP客户端（用于模拟请求）

## 项目结构规划

```
mapboxgl-vue3/
├── public/
│   ├── mock-data/
│   │   ├── response.json          # 模拟API响应
│   │   └── workout-metadata.json  # 运动元数据
│   ├── geo-data/
│   │   ├── track-points.geojson   # 轨迹坐标点
│   │   ├── elevation-profile.json # 高程数据
│   │   └── sensor-data.json       # 传感器数据
│   └── assets/
│       └── terrain-variance.png   # 地形方差图
├── src/
│   ├── components/
│   │   ├── Map/
│   │   │   ├── MapView.vue        # 地图容器
│   │   │   └── MapControls.vue    # 地图控制
│   │   └── UI/
│   │       └── PlayButton.vue     # 播放按钮
│   ├── composables/
│   │   ├── usePosition.ts         # 动画位置控制
│   │   ├── useMapbox.ts          # 地图集成
│   │   ├── useWebGL.ts           # WebGL渲染
│   │   ├── useCamera3D.ts        # 3D相机
│   │   └── useDataLoader.ts      # 数据加载
│   ├── stores/
│   │   ├── animation.ts          # 动画状态
│   │   ├── track.ts              # 轨迹数据
│   │   └── map.ts                # 地图状态
│   ├── services/
│   │   ├── DataService.ts        # 数据服务
│   │   ├── WebGLRenderer.ts      # WebGL渲染器
│   │   ├── CameraAnimator.ts     # 相机动画
│   │   └── TrackProcessor.ts     # 轨迹处理
│   ├── utils/
│   │   ├── catmullRom.ts         # 样条插值
│   │   ├── animation.ts          # 动画工具
│   │   ├── math.ts               # 数学计算
│   │   └── geoUtils.ts           # 地理工具
│   ├── types/
│   │   ├── track.ts              # 轨迹类型
│   │   ├── animation.ts          # 动画类型
│   │   └── api.ts                # API类型
│   └── config/
│       ├── mapbox.ts             # Mapbox配置
│       └── constants.ts          # 常量定义
├── docs/                         # 项目文档
└── tests/                        # 测试文件
```

## 数据流架构

### 数据加载流程
1. **应用启动** → 加载配置和静态资源
2. **数据服务** → 从public目录加载模拟数据
3. **数据处理** → 解析和优化轨迹数据
4. **状态管理** → 存储到Pinia stores
5. **组件渲染** → 响应式更新UI

### 现有数据文件结构
```
public/
├── mock-data/
│   └── response.json          # 完整的运动数据响应（包含扩展数据）
└── geo-data/
    ├── workout_track.geojson  # GeoJSON格式轨迹数据
    ├── workout_track.csv      # CSV格式轨迹数据
    └── workout_track.gpx      # GPX格式轨迹数据
```

### 模拟API设计
```typescript
// DataService.ts - 模拟后端API
class DataService {
  // 加载运动数据响应
  async loadWorkoutResponse(): Promise<ApiResponse>

  // 加载轨迹坐标（GeoJSON）
  async loadTrackPoints(): Promise<GeoJSON.FeatureCollection>

  // 加载CSV格式轨迹
  async loadTrackCSV(): Promise<string>

  // 加载GPX格式轨迹
  async loadTrackGPX(): Promise<string>

  // 提取传感器数据（高程、心率等）
  async getSensorData(): Promise<SensorData[]>
}
```

## 开发工具链

### 构建工具
- **Vite** - 开发服务器和构建
- **Rollup** - 生产构建优化
- **ESBuild** - 快速编译

### 代码质量
- **ESLint** - 代码规范检查
- **Prettier** - 代码格式化
- **TypeScript** - 类型检查

### 测试框架
- **Vitest** - 单元测试
- **Vue Test Utils** - Vue组件测试
- **Playwright** - E2E测试

### 开发体验
- **Vue DevTools** - Vue调试工具
- **Vite DevTools** - Vite开发工具
- **TypeScript Language Server** - IDE支持

## 性能优化策略

### 数据优化
- **懒加载** - 按需加载数据文件
- **数据缓存** - 内存缓存处理结果
- **数据压缩** - GZip压缩静态文件

### 渲染优化
- **WebGL批处理** - 减少绘制调用
- **LOD控制** - 分级细节渲染
- **视锥体裁剪** - 只渲染可见部分

### 内存管理
- **对象池** - 复用WebGL对象
- **垃圾回收** - 及时清理无用对象
- **内存监控** - 开发时内存使用监控

## 部署策略

### 静态部署
- **Vercel** - 推荐的静态部署平台
- **Netlify** - 备选部署平台
- **GitHub Pages** - 开源项目部署

### 资源优化
- **代码分割** - 按路由分割代码
- **资源压缩** - 图片和文件压缩
- **CDN加速** - 静态资源CDN分发

## 开发阶段规划

### 第一阶段：基础架构
- 搭建Vue3 + Vite项目
- 配置TypeScript和工具链
- 创建基础组件结构
- 实现模拟数据加载

### 第二阶段：地图集成
- 集成Mapbox GL JS
- 实现3D地形渲染
- 加载和显示轨迹数据
- 基础地图交互

### 第三阶段：动画系统
- 实现3D相机动画
- 轨迹播放控制
- 动画状态管理
- 性能优化

### 第四阶段：WebGL渲染
- 自定义WebGL图层
- 高性能轨迹渲染
- 视觉效果优化
- 最终性能调优

这个技术栈规划确保了项目的可扩展性、可维护性和高性能，同时保持了开发的灵活性。
