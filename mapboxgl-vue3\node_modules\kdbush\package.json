{"name": "kdbush", "version": "4.0.2", "description": "A very fast static 2D index for points based on kd-tree.", "type": "module", "main": "kdbush.js", "module": "index.js", "exports": "./index.js", "types": "index.d.ts", "sideEffects": false, "repository": {"type": "git", "url": "git://github.com/mourner/kdbush.git"}, "devDependencies": {"@rollup/plugin-terser": "^0.4.1", "eslint": "^8.38.0", "eslint-config-mourner": "^3.0.0", "rollup": "^3.20.6", "typescript": "^5.0.4"}, "scripts": {"pretest": "eslint index.js test.js bench.js rollup.config.js", "test": "tsc && node test.js", "bench": "node bench.js", "build": "rollup -c", "prepublishOnly": "npm run test && npm run build"}, "eslintConfig": {"extends": "mourner"}, "keywords": ["index", "points", "kd-tree", "data structures", "algorithms", "spatial", "geometry"], "files": ["kdbush.js", "kdbush.min.js", "index.js", "index.d.ts"], "author": "<PERSON>", "license": "ISC"}