# MapboxGL Vue3 系统架构图

## 整体系统架构

```mermaid
graph TB
    subgraph "用户界面层 (Vue3)"
        A[MapView.vue<br/>地图容器组件]
        B[PlayButton.vue<br/>播放控制按钮]
    end
    
    subgraph "状态管理层 (Pinia)"
        C[useAnimationStore<br/>动画状态管理]
        D[useTrackStore<br/>轨迹数据管理]
    end
    
    subgraph "组合式API层 (Composables)"
        E[usePosition<br/>动画位置控制]
        F[useWebGLTrack<br/>WebGL轨迹渲染]
        G[useCamera3D<br/>3D相机控制]
    end
    
    subgraph "核心服务层 (Services)"
        H[WebGLTrackRenderer<br/>WebGL渲染器]
        I[CameraAnimator<br/>相机动画器]
        J[TrackProcessor<br/>轨迹处理器]
        K[MapboxService<br/>地图服务]
    end
    
    subgraph "算法核心层 (Utils)"
        L[catmullRom.ts<br/>样条插值算法]
        M[animation.ts<br/>动画工具函数]
        N[math.ts<br/>数学计算工具]
    end
    
    %% 连接关系
    A --> C
    A --> D
    B --> C
    
    C --> E
    D --> F
    E --> G
    
    E --> I
    F --> H
    G --> I
    F --> K
    
    H --> N
    I --> L
    I --> M
    J --> N
    K --> N
    
    %% 样式 - Dark主题适配
    classDef uiLayer fill:#2d1b69,stroke:#8b5cf6,stroke-width:2px,color:#ffffff
    classDef stateLayer fill:#1e3a2e,stroke:#10b981,stroke-width:2px,color:#ffffff
    classDef composableLayer fill:#451a03,stroke:#f59e0b,stroke-width:2px,color:#ffffff
    classDef serviceLayer fill:#1e3a8a,stroke:#3b82f6,stroke-width:2px,color:#ffffff
    classDef utilLayer fill:#374151,stroke:#9ca3af,stroke-width:2px,color:#ffffff
    
    class A,B uiLayer
    class C,D stateLayer
    class E,F,G composableLayer
    class H,I,J,K serviceLayer
    class L,M,N utilLayer
```

## 架构层次说明

### 1. 用户界面层 (Vue3)
- **MapView.vue**: 地图容器组件，承载Mapbox地图实例
- **PlayButton.vue**: 播放控制按钮，控制动画播放/暂停

### 2. 状态管理层 (Pinia)
- **useAnimationStore**: 管理动画状态（播放/暂停、进度、时间）
- **useTrackStore**: 管理轨迹数据（GPS坐标、处理后的数据）

### 3. 组合式API层 (Composables)
- **usePosition**: 核心动画控制，40秒循环逻辑
- **useWebGLTrack**: WebGL轨迹渲染控制
- **useCamera3D**: 3D相机动画控制

### 4. 核心服务层 (Services)
- **WebGLTrackRenderer**: WebGL自定义图层渲染器
- **CameraAnimator**: 3D相机动画引擎
- **TrackProcessor**: 轨迹数据处理器
- **MapboxService**: Mapbox地图服务封装

### 5. 算法核心层 (Utils)
- **catmullRom.ts**: Catmull-Rom样条插值算法
- **animation.ts**: 动画相关工具函数
- **math.ts**: 数学计算工具函数

## 数据流向

```mermaid
flowchart LR
    A[用户操作] --> B[Vue组件]
    B --> C[Pinia Store]
    C --> D[Composables]
    D --> E[Services]
    E --> F[Utils算法]
    F --> G[WebGL渲染]
    G --> H[地图显示]
```

## 核心技术栈

```mermaid
graph TD
    subgraph "前端框架"
        A1[Vue 3.4+]
        A2[Vite 5+]
        A3[TypeScript 5+]
    end
    
    subgraph "状态管理"
        B1[Pinia 2+]
        B2[Composables]
    end
    
    subgraph "地图渲染"
        C1[Mapbox GL JS 3+]
        C2[WebGL API]
        C3[自定义着色器]
    end
    
    subgraph "数学算法"
        D1[gl-matrix]
        D2[Catmull-Rom插值]
        D3[Douglas-Peucker简化]
    end
    
    A1 --> B1
    B1 --> C1
    C1 --> C2
    C2 --> D1
```

## 关键特性

### 高性能渲染
- WebGL自定义着色器
- 批处理数据更新
- Float32Array预分配
- 距离优先更新算法

### 3D相机系统
- Catmull-Rom样条插值
- 电影级相机运动
- 球坐标系定位
- 向心参数化

### 动画控制
- 40秒固定时长循环
- requestAnimationFrame驱动
- 精确时间计算
- 播放/暂停控制

### 数据处理
- GPS坐标解析
- 轨迹简化算法
- 时间同步对齐
- 数据优化缓存
