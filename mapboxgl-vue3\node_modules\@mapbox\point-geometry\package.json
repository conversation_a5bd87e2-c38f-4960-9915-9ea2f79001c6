{"name": "@mapbox/point-geometry", "version": "1.1.0", "description": "a point geometry with transforms", "main": "index.js", "type": "module", "exports": "./index.js", "types": "index.d.ts", "scripts": {"lint": "eslint --fix index.js test.js", "pretest": "npm run lint", "test": "tsc && node test.js", "doc": "documentation readme index.js --section=API --markdown-toc=false", "cov": "node --test --experimental-test-coverage"}, "repository": {"type": "git", "url": "**************:mapbox/point-geometry.git"}, "keywords": ["point", "geometry", "primitive"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/mapbox/point-geometry/issues"}, "homepage": "https://github.com/mapbox/point-geometry", "devDependencies": {"documentation": "^14.0.3", "eslint": "^9.6.0", "eslint-config-mourner": "^4.0.2", "typescript": "^5.5.3"}, "files": ["index.d.ts"]}