https://github.com/deanm/css-color-parser-js

JavaScript parser for CSS color strings.

> parseCSSColor(' rgba (255, 128, 12, 0.5)');
 [ 255, 128, 12, 0.5 ]
> parseCSSColor('#fff');
 [ 255, 255, 255, 1 ]
> parseCSSColor('#ff0011');
 [ 255, 0, 17, 1 ]
> parseCSSColor('slateblue');
 [ 106, 90, 205, 1 ]
> parseCSSColor('blah');
 null
> parseCSSColor('ffffff');
 null
> parseCSSColor('hsla(900, 15%, 90%, 0.5)')
 [ 226, 233, 233, 0.5 ]
> parseCSSColor('hsla(900, 15%, 90%)')
 null
> parseCSSColor('hsl(900, 15%, 90%)')
 [ 226, 233, 233, 1 ]
> parseCSSColor('hsl(900, 0.15, 90%)')  // NOTE: not spec compliant.
 [ 226, 233, 233, 1 ]


(c) <PERSON> <<EMAIL>>, 2012.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to
deal in the Software without restriction, including without limitation the
rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
sell copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
IN THE SOFTWARE.
