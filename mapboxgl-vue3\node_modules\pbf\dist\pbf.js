!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):(t="undefined"!=typeof globalThis?globalThis:t||self).Pbf=i()}(this,(function(){"use strict";const t=4294967296,i=1/t,e="undefined"==typeof TextDecoder?null:new TextDecoder("utf-8");function s(t,i,e){return e?4294967296*i+(t>>>0):4294967296*(i>>>0)+(t>>>0)}function r(t,i,e){const s=i<=16383?1:i<=2097151?2:i<=268435455?3:Math.floor(Math.log(i)/(7*Math.LN2));e.realloc(s);for(let i=e.pos-1;i>=t;i--)e.buf[i+s]=e.buf[i]}function o(t,i){for(let e=0;e<t.length;e++)i.writeVarint(t[e])}function h(t,i){for(let e=0;e<t.length;e++)i.writeSVarint(t[e])}function n(t,i){for(let e=0;e<t.length;e++)i.writeFloat(t[e])}function a(t,i){for(let e=0;e<t.length;e++)i.writeDouble(t[e])}function d(t,i){for(let e=0;e<t.length;e++)i.writeBoolean(t[e])}function l(t,i){for(let e=0;e<t.length;e++)i.writeFixed32(t[e])}function u(t,i){for(let e=0;e<t.length;e++)i.writeSFixed32(t[e])}function f(t,i){for(let e=0;e<t.length;e++)i.writeFixed64(t[e])}function p(t,i){for(let e=0;e<t.length;e++)i.writeSFixed64(t[e])}return class{constructor(t=new Uint8Array(16)){this.buf=ArrayBuffer.isView(t)?t:new Uint8Array(t),this.dataView=new DataView(this.buf.buffer),this.pos=0,this.type=0,this.length=this.buf.length}readFields(t,i,e=this.length){for(;this.pos<e;){const e=this.readVarint(),s=e>>3,r=this.pos;this.type=7&e,t(s,i,this),this.pos===r&&this.skip(e)}return i}readMessage(t,i){return this.readFields(t,i,this.readVarint()+this.pos)}readFixed32(){const t=this.dataView.getUint32(this.pos,!0);return this.pos+=4,t}readSFixed32(){const t=this.dataView.getInt32(this.pos,!0);return this.pos+=4,t}readFixed64(){const i=this.dataView.getUint32(this.pos,!0)+this.dataView.getUint32(this.pos+4,!0)*t;return this.pos+=8,i}readSFixed64(){const i=this.dataView.getUint32(this.pos,!0)+this.dataView.getInt32(this.pos+4,!0)*t;return this.pos+=8,i}readFloat(){const t=this.dataView.getFloat32(this.pos,!0);return this.pos+=4,t}readDouble(){const t=this.dataView.getFloat64(this.pos,!0);return this.pos+=8,t}readVarint(t){const i=this.buf;let e,r;return r=i[this.pos++],e=127&r,r<128?e:(r=i[this.pos++],e|=(127&r)<<7,r<128?e:(r=i[this.pos++],e|=(127&r)<<14,r<128?e:(r=i[this.pos++],e|=(127&r)<<21,r<128?e:(r=i[this.pos],e|=(15&r)<<28,function(t,i,e){const r=e.buf;let o,h;if(h=r[e.pos++],o=(112&h)>>4,h<128)return s(t,o,i);if(h=r[e.pos++],o|=(127&h)<<3,h<128)return s(t,o,i);if(h=r[e.pos++],o|=(127&h)<<10,h<128)return s(t,o,i);if(h=r[e.pos++],o|=(127&h)<<17,h<128)return s(t,o,i);if(h=r[e.pos++],o|=(127&h)<<24,h<128)return s(t,o,i);if(h=r[e.pos++],o|=(1&h)<<31,h<128)return s(t,o,i);throw new Error("Expected varint not more than 10 bytes")}(e,t,this)))))}readVarint64(){return this.readVarint(!0)}readSVarint(){const t=this.readVarint();return t%2==1?(t+1)/-2:t/2}readBoolean(){return Boolean(this.readVarint())}readString(){const t=this.readVarint()+this.pos,i=this.pos;return this.pos=t,t-i>=12&&e?e.decode(this.buf.subarray(i,t)):function(t,i,e){let s="",r=i;for(;r<e;){const i=t[r];let o,h,n,a=null,d=i>239?4:i>223?3:i>191?2:1;if(r+d>e)break;1===d?i<128&&(a=i):2===d?(o=t[r+1],128==(192&o)&&(a=(31&i)<<6|63&o,a<=127&&(a=null))):3===d?(o=t[r+1],h=t[r+2],128==(192&o)&&128==(192&h)&&(a=(15&i)<<12|(63&o)<<6|63&h,(a<=2047||a>=55296&&a<=57343)&&(a=null))):4===d&&(o=t[r+1],h=t[r+2],n=t[r+3],128==(192&o)&&128==(192&h)&&128==(192&n)&&(a=(15&i)<<18|(63&o)<<12|(63&h)<<6|63&n,(a<=65535||a>=1114112)&&(a=null))),null===a?(a=65533,d=1):a>65535&&(a-=65536,s+=String.fromCharCode(a>>>10&1023|55296),a=56320|1023&a),s+=String.fromCharCode(a),r+=d}return s}(this.buf,i,t)}readBytes(){const t=this.readVarint()+this.pos,i=this.buf.subarray(this.pos,t);return this.pos=t,i}readPackedVarint(t=[],i){const e=this.readPackedEnd();for(;this.pos<e;)t.push(this.readVarint(i));return t}readPackedSVarint(t=[]){const i=this.readPackedEnd();for(;this.pos<i;)t.push(this.readSVarint());return t}readPackedBoolean(t=[]){const i=this.readPackedEnd();for(;this.pos<i;)t.push(this.readBoolean());return t}readPackedFloat(t=[]){const i=this.readPackedEnd();for(;this.pos<i;)t.push(this.readFloat());return t}readPackedDouble(t=[]){const i=this.readPackedEnd();for(;this.pos<i;)t.push(this.readDouble());return t}readPackedFixed32(t=[]){const i=this.readPackedEnd();for(;this.pos<i;)t.push(this.readFixed32());return t}readPackedSFixed32(t=[]){const i=this.readPackedEnd();for(;this.pos<i;)t.push(this.readSFixed32());return t}readPackedFixed64(t=[]){const i=this.readPackedEnd();for(;this.pos<i;)t.push(this.readFixed64());return t}readPackedSFixed64(t=[]){const i=this.readPackedEnd();for(;this.pos<i;)t.push(this.readSFixed64());return t}readPackedEnd(){return 2===this.type?this.readVarint()+this.pos:this.pos+1}skip(t){const i=7&t;if(0===i)for(;this.buf[this.pos++]>127;);else if(2===i)this.pos=this.readVarint()+this.pos;else if(5===i)this.pos+=4;else{if(1!==i)throw new Error(`Unimplemented type: ${i}`);this.pos+=8}}writeTag(t,i){this.writeVarint(t<<3|i)}realloc(t){let i=this.length||16;for(;i<this.pos+t;)i*=2;if(i!==this.length){const t=new Uint8Array(i);t.set(this.buf),this.buf=t,this.dataView=new DataView(t.buffer),this.length=i}}finish(){return this.length=this.pos,this.pos=0,this.buf.subarray(0,this.length)}writeFixed32(t){this.realloc(4),this.dataView.setInt32(this.pos,t,!0),this.pos+=4}writeSFixed32(t){this.realloc(4),this.dataView.setInt32(this.pos,t,!0),this.pos+=4}writeFixed64(t){this.realloc(8),this.dataView.setInt32(this.pos,-1&t,!0),this.dataView.setInt32(this.pos+4,Math.floor(t*i),!0),this.pos+=8}writeSFixed64(t){this.realloc(8),this.dataView.setInt32(this.pos,-1&t,!0),this.dataView.setInt32(this.pos+4,Math.floor(t*i),!0),this.pos+=8}writeVarint(t){(t=+t||0)>268435455||t<0?function(t,i){let e,s;t>=0?(e=t%4294967296|0,s=t/4294967296|0):(e=~(-t%4294967296),s=~(-t/4294967296),4294967295^e?e=e+1|0:(e=0,s=s+1|0));if(t>=0x10000000000000000||t<-0x10000000000000000)throw new Error("Given varint doesn't fit into 10 bytes");i.realloc(10),function(t,i,e){e.buf[e.pos++]=127&t|128,t>>>=7,e.buf[e.pos++]=127&t|128,t>>>=7,e.buf[e.pos++]=127&t|128,t>>>=7,e.buf[e.pos++]=127&t|128,t>>>=7,e.buf[e.pos]=127&t}(e,0,i),function(t,i){const e=(7&t)<<4;if(i.buf[i.pos++]|=e|((t>>>=3)?128:0),!t)return;if(i.buf[i.pos++]=127&t|((t>>>=7)?128:0),!t)return;if(i.buf[i.pos++]=127&t|((t>>>=7)?128:0),!t)return;if(i.buf[i.pos++]=127&t|((t>>>=7)?128:0),!t)return;if(i.buf[i.pos++]=127&t|((t>>>=7)?128:0),!t)return;i.buf[i.pos++]=127&t}(s,i)}(t,this):(this.realloc(4),this.buf[this.pos++]=127&t|(t>127?128:0),t<=127||(this.buf[this.pos++]=127&(t>>>=7)|(t>127?128:0),t<=127||(this.buf[this.pos++]=127&(t>>>=7)|(t>127?128:0),t<=127||(this.buf[this.pos++]=t>>>7&127))))}writeSVarint(t){this.writeVarint(t<0?2*-t-1:2*t)}writeBoolean(t){this.writeVarint(+t)}writeString(t){t=String(t),this.realloc(4*t.length),this.pos++;const i=this.pos;this.pos=function(t,i,e){for(let s,r,o=0;o<i.length;o++){if(s=i.charCodeAt(o),s>55295&&s<57344){if(!r){s>56319||o+1===i.length?(t[e++]=239,t[e++]=191,t[e++]=189):r=s;continue}if(s<56320){t[e++]=239,t[e++]=191,t[e++]=189,r=s;continue}s=r-55296<<10|s-56320|65536,r=null}else r&&(t[e++]=239,t[e++]=191,t[e++]=189,r=null);s<128?t[e++]=s:(s<2048?t[e++]=s>>6|192:(s<65536?t[e++]=s>>12|224:(t[e++]=s>>18|240,t[e++]=s>>12&63|128),t[e++]=s>>6&63|128),t[e++]=63&s|128)}return e}(this.buf,t,this.pos);const e=this.pos-i;e>=128&&r(i,e,this),this.pos=i-1,this.writeVarint(e),this.pos+=e}writeFloat(t){this.realloc(4),this.dataView.setFloat32(this.pos,t,!0),this.pos+=4}writeDouble(t){this.realloc(8),this.dataView.setFloat64(this.pos,t,!0),this.pos+=8}writeBytes(t){const i=t.length;this.writeVarint(i),this.realloc(i);for(let e=0;e<i;e++)this.buf[this.pos++]=t[e]}writeRawMessage(t,i){this.pos++;const e=this.pos;t(i,this);const s=this.pos-e;s>=128&&r(e,s,this),this.pos=e-1,this.writeVarint(s),this.pos+=s}writeMessage(t,i,e){this.writeTag(t,2),this.writeRawMessage(i,e)}writePackedVarint(t,i){i.length&&this.writeMessage(t,o,i)}writePackedSVarint(t,i){i.length&&this.writeMessage(t,h,i)}writePackedBoolean(t,i){i.length&&this.writeMessage(t,d,i)}writePackedFloat(t,i){i.length&&this.writeMessage(t,n,i)}writePackedDouble(t,i){i.length&&this.writeMessage(t,a,i)}writePackedFixed32(t,i){i.length&&this.writeMessage(t,l,i)}writePackedSFixed32(t,i){i.length&&this.writeMessage(t,u,i)}writePackedFixed64(t,i){i.length&&this.writeMessage(t,f,i)}writePackedSFixed64(t,i){i.length&&this.writeMessage(t,p,i)}writeBytesField(t,i){this.writeTag(t,2),this.writeBytes(i)}writeFixed32Field(t,i){this.writeTag(t,5),this.writeFixed32(i)}writeSFixed32Field(t,i){this.writeTag(t,5),this.writeSFixed32(i)}writeFixed64Field(t,i){this.writeTag(t,1),this.writeFixed64(i)}writeSFixed64Field(t,i){this.writeTag(t,1),this.writeSFixed64(i)}writeVarintField(t,i){this.writeTag(t,0),this.writeVarint(i)}writeSVarintField(t,i){this.writeTag(t,0),this.writeSVarint(i)}writeStringField(t,i){this.writeTag(t,2),this.writeString(i)}writeFloatField(t,i){this.writeTag(t,5),this.writeFloat(i)}writeDoubleField(t,i){this.writeTag(t,1),this.writeDouble(i)}writeBooleanField(t,i){this.writeVarintField(t,+i)}}}));
