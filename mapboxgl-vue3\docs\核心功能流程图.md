# MapboxGL Vue3 核心功能流程图

## 整体功能流程

```mermaid
flowchart TD
    Start([应用启动]) --> Init[Vue3应用初始化]
    Init --> MapInit[Mapbox地图初始化]
    
    %% 第一阶段：地图集成
    MapInit --> TerrainSetup[3D地形系统设置]
    MapInit --> DEMLoad[DEM数据源加载]
    MapInit --> TerrainVariance[地形方差图集成<br/>蓝色通道高程]
    
    TerrainSetup --> DataProcess[轨迹数据处理]
    DEMLoad --> DataProcess
    TerrainVariance --> DataProcess
    
    %% 第二阶段：数据处理
    DataProcess --> GPSParse[GPS坐标解析]
    DataProcess --> DataOptimize[数据优化算法<br/>Douglas-Peucker]
    DataProcess --> Interpolation[轨迹点插值]
    DataProcess --> TimeSync[时间同步对齐]
    
    GPSParse --> CameraSystem[3D相机动画系统]
    DataOptimize --> CameraSystem
    Interpolation --> CameraSystem
    TimeSync --> CameraSystem
    
    %% 第三阶段：相机系统
    CameraSystem --> CatmullRom[Catmull-Rom<br/>样条插值]
    CameraSystem --> CameraPath[相机路径生成]
    CameraSystem --> SphereCoord[球坐标系定位]
    CameraSystem --> SmoothMotion[电影级相机运动]
    
    CatmullRom --> AnimationControl[动画控制系统]
    CameraPath --> AnimationControl
    SphereCoord --> AnimationControl
    SmoothMotion --> AnimationControl
    
    %% 第四阶段：动画控制
    AnimationControl --> UsePosition[usePosition<br/>动画核心]
    AnimationControl --> AnimationLoop[40秒动画循环]
    AnimationControl --> PlayControl[播放/暂停控制]
    AnimationControl --> RAFDriver[requestAnimationFrame<br/>驱动]
    
    UsePosition --> WebGLRender[WebGL自定义图层渲染]
    AnimationLoop --> WebGLRender
    PlayControl --> WebGLRender
    RAFDriver --> WebGLRender
    
    %% 第五阶段：WebGL渲染
    WebGLRender --> CustomLayer[Mapbox自定义图层]
    WebGLRender --> WebGLShader[WebGL着色器]
    WebGLRender --> BatchRender[批处理渲染]
    WebGLRender --> Performance[性能优化]
    
    CustomLayer --> Display[地图显示]
    WebGLShader --> Display
    BatchRender --> Display
    Performance --> Display
    
    %% 样式 - Dark主题适配
    classDef startEnd fill:#1e3a2e,stroke:#10b981,stroke-width:3px,color:#ffffff
    classDef phase1 fill:#2d1b69,stroke:#8b5cf6,stroke-width:2px,color:#ffffff
    classDef phase2 fill:#451a03,stroke:#f59e0b,stroke-width:2px,color:#ffffff
    classDef phase3 fill:#1e3a8a,stroke:#3b82f6,stroke-width:2px,color:#ffffff
    classDef phase4 fill:#14532d,stroke:#22c55e,stroke-width:2px,color:#ffffff
    classDef phase5 fill:#374151,stroke:#9ca3af,stroke-width:2px,color:#ffffff
    
    class Start,Display startEnd
    class MapInit,TerrainSetup,DEMLoad,TerrainVariance phase1
    class DataProcess,GPSParse,DataOptimize,Interpolation,TimeSync phase2
    class CameraSystem,CatmullRom,CameraPath,SphereCoord,SmoothMotion phase3
    class AnimationControl,UsePosition,AnimationLoop,PlayControl,RAFDriver phase4
    class WebGLRender,CustomLayer,WebGLShader,BatchRender,Performance phase5
```

## 分阶段详细流程

### 第一阶段：Mapbox地图集成

```mermaid
flowchart LR
    A[Vue3组件挂载] --> B[创建Mapbox实例]
    B --> C[配置地图样式]
    C --> D[添加DEM数据源]
    D --> E[设置地形夸张度]
    E --> F[加载地形方差图]
    F --> G[蓝色通道高程映射]
```

### 第二阶段：3D相机动画系统

```mermaid
flowchart TD
    A[移植React代码] --> B[CatmullRomSpline类]
    B --> C[CatmullRomSegment处理]
    C --> D[向心参数化]
    D --> E[样条插值计算]
    E --> F[相机路径生成]
    F --> G[球坐标系转换]
    G --> H[电影级运动效果]
```

### 第三阶段：轨迹数据处理

```mermaid
flowchart LR
    A[原始GPS数据] --> B[坐标解析]
    B --> C[数据验证]
    C --> D[Douglas-Peucker简化]
    D --> E[轨迹点插值]
    E --> F[时间同步]
    F --> G[数据缓存]
```

### 第四阶段：动画控制

```mermaid
flowchart TD
    A[usePosition初始化] --> B[动画状态管理]
    B --> C[40秒时长设置]
    C --> D[requestAnimationFrame启动]
    D --> E[时间进度计算]
    E --> F[播放/暂停控制]
    F --> G[动画循环检测]
    G --> H[状态更新通知]
```

### 第五阶段：WebGL自定义渲染

```mermaid
flowchart LR
    A[自定义图层创建] --> B[WebGL上下文]
    B --> C[着色器编译]
    C --> D[顶点数据准备]
    D --> E[批处理更新]
    E --> F[距离优先渲染]
    F --> G[性能优化]
```

## 核心循环流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Vue as Vue组件
    participant Store as Pinia Store
    participant Anim as 动画控制
    participant Camera as 相机系统
    participant WebGL as WebGL渲染
    participant Map as 地图显示
    
    User->>Vue: 点击播放
    Vue->>Store: 更新播放状态
    Store->>Anim: 启动动画循环
    
    loop 40秒动画循环
        Anim->>Anim: requestAnimationFrame
        Anim->>Store: 更新进度
        Store->>Camera: 计算相机位置
        Camera->>WebGL: 更新渲染数据
        WebGL->>Map: 渲染轨迹
    end
    
    Anim->>Store: 动画完成
    Store->>Vue: 更新UI状态
```

## 性能优化流程

```mermaid
flowchart TD
    A[数据预处理] --> B[Float32Array预分配]
    B --> C[批处理更新]
    C --> D[距离优先排序]
    D --> E[视锥体裁剪]
    E --> F[LOD细节控制]
    F --> G[内存池管理]
    G --> H[垃圾回收优化]
```

## 关键时间节点

1. **T0**: 应用启动，Vue3初始化
2. **T1**: Mapbox地图加载完成
3. **T2**: 3D地形系统就绪
4. **T3**: 轨迹数据处理完成
5. **T4**: 3D相机路径生成完成
6. **T5**: 动画系统准备就绪
7. **T6**: WebGL渲染器初始化完成
8. **T7**: 首次渲染完成，用户可交互

## 数据流转图

```mermaid
flowchart LR
    A[GPS原始数据] --> B[数据解析器]
    B --> C[轨迹简化]
    C --> D[插值处理]
    D --> E[相机路径计算]
    E --> F[动画状态]
    F --> G[WebGL缓冲区]
    G --> H[着色器渲染]
    H --> I[屏幕显示]
```
