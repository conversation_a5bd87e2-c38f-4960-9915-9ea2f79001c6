{"name": "geojson-vt", "version": "4.0.2", "description": "Slice GeoJSON data into vector tiles efficiently", "homepage": "https://github.com/mapbox/geojson-vt", "type": "module", "exports": "./src/index.js", "sideEffects": false, "keywords": ["spatial", "g<PERSON><PERSON><PERSON>", "tiles", "geometry"], "author": "<PERSON>", "repository": {"type": "git", "url": "git://github.com/mapbox/geojson-vt.git"}, "module": "src/index.js", "main": "geojson-vt-dev.js", "jsdelivr": "geo<PERSON>son-vt.js", "unpkg": "geo<PERSON>son-vt.js", "devDependencies": {"@rollup/plugin-terser": "^0.4.4", "benchmark": "^2.1.4", "c8": "^10.1.1", "eslint": "^8.57.0", "eslint-config-mourner": "^3.0.0", "rollup": "^3.29.3"}, "eslintConfig": {"extends": "mourner", "parserOptions": {"ecmaVersion": 2020}, "globals": {"topojson": true}}, "license": "ISC", "scripts": {"pretest": "eslint src/*.js test/*.js debug/viz.js", "test": "c8 node --test", "build": "rollup -c", "watch": "rollup -cw", "prepublishOnly": "npm run test && npm run build"}, "files": ["geo<PERSON>son-vt.js", "geojson-vt-dev.js", "src"]}