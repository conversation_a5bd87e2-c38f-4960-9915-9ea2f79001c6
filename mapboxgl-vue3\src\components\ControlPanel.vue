<template>
  <div class="control-panel">
    <PlayButton />
  </div>
</template>

<script setup lang="ts">
import PlayButton from './PlayButton.vue'
</script>

<style scoped>
.control-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 10vh;
  min-height: 80px;
  background-color: #333333;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
</style>
