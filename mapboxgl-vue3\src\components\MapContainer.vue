<template>
  <div class="map-container">
    <div id="mapbox-map" class="map"></div>
    <div class="map-overlay">
      <div class="map-info">
        <span class="map-status">{{ mapStatus }}</span>
        <span v-if="error" class="map-error">{{ error }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import mapboxgl from 'mapbox-gl'
import { mapboxConfig, validateConfig, getTerrainSourceConfig, getTerrainLayerConfig } from '@/config/mapbox'
import { useDataLoader } from '@/composables/useDataLoader'

// 响应式状态
const mapStatus = ref('地图初始化中...')
const error = ref<string | null>(null)
const mapInstance = ref<mapboxgl.Map | null>(null)

// 数据加载器
const { loadAllData, trackData } = useDataLoader()

onMounted(async () => {
  try {
    // 验证配置
    if (!validateConfig()) {
      throw new Error('Mapbox配置验证失败，请检查环境变量')
    }

    // 设置访问令牌
    mapboxgl.accessToken = mapboxConfig.accessToken

    mapStatus.value = '创建地图实例...'

    // 创建地图实例
    const map = new mapboxgl.Map({
      container: 'mapbox-map',
      style: mapboxConfig.style,
      center: [24.9384, 60.1699], // 默认中心点 (赫尔辛基)
      zoom: 10,
      pitch: 45,
      bearing: 0,
      antialias: true
    })

    mapInstance.value = map

    // 地图加载完成事件
    map.on('load', async () => {
      try {
        mapStatus.value = '配置地形...'

        // 添加地形数据源
        map.addSource('mapbox-dem', getTerrainSourceConfig() as mapboxgl.RasterDEMSourceSpecification)

        // 设置地形
        map.setTerrain(getTerrainLayerConfig() as mapboxgl.TerrainSpecification)

        mapStatus.value = '加载轨迹数据...'

        // 加载数据
        await loadAllData()

        // 如果有轨迹数据，添加到地图并调整视图
        if (trackData.value) {
          // 添加轨迹数据源
          map.addSource('track-data', {
            type: 'geojson',
            data: {
              type: 'LineString',
              coordinates: trackData.value.points.map(point => [point.lng, point.lat, point.elevation || 0])
            }
          })

          // 添加轨迹线图层
          map.addLayer({
            id: 'track-line',
            type: 'line',
            source: 'track-data',
            layout: {
              'line-join': 'round',
              'line-cap': 'round'
            },
            paint: {
              'line-color': '#ff6b35',
              'line-width': 4,
              'line-opacity': 0.8
            }
          })

          // 添加轨迹起点标记
          const startPoint = trackData.value.points[0]
          if (startPoint) {
            map.addSource('start-point', {
              type: 'geojson',
              data: {
                type: 'Point',
                coordinates: [startPoint.lng, startPoint.lat]
              }
            })

            map.addLayer({
              id: 'start-marker',
              type: 'circle',
              source: 'start-point',
              paint: {
                'circle-radius': 8,
                'circle-color': '#00ff00',
                'circle-stroke-color': '#ffffff',
                'circle-stroke-width': 2
              }
            })
          }

          // 添加轨迹终点标记
          const endPoint = trackData.value.points[trackData.value.points.length - 1]
          if (endPoint) {
            map.addSource('end-point', {
              type: 'geojson',
              data: {
                type: 'Point',
                coordinates: [endPoint.lng, endPoint.lat]
              }
            })

            map.addLayer({
              id: 'end-marker',
              type: 'circle',
              source: 'end-point',
              paint: {
                'circle-radius': 8,
                'circle-color': '#ff0000',
                'circle-stroke-color': '#ffffff',
                'circle-stroke-width': 2
              }
            })
          }

          // 调整视图到轨迹范围
          const bounds = new mapboxgl.LngLatBounds()
          trackData.value.points.forEach(point => {
            bounds.extend([point.lng, point.lat])
          })

          map.fitBounds(bounds, {
            padding: 50,
            duration: 2000
          })

          console.log(`轨迹已添加到地图，共 ${trackData.value.points.length} 个点`)
        }

        mapStatus.value = '地图已就绪'
        console.log('Mapbox地图初始化完成')

      } catch (err) {
        console.error('地图配置失败:', err)
        error.value = err instanceof Error ? err.message : '地图配置失败'
        mapStatus.value = '地图配置失败'
      }
    })

    // 地图错误事件
    map.on('error', (e) => {
      console.error('Mapbox地图错误:', e.error)
      error.value = e.error.message
      mapStatus.value = '地图加载失败'
    })

  } catch (err) {
    console.error('地图初始化失败:', err)
    error.value = err instanceof Error ? err.message : '地图初始化失败'
    mapStatus.value = '地图初始化失败'
  }
})

onUnmounted(() => {
  // 清理地图实例
  if (mapInstance.value) {
    mapInstance.value.remove()
    mapInstance.value = null
  }
})
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.map {
  width: 100%;
  height: 100%;
}

.map-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  color: #666666;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.map-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.map-status {
  font-weight: 500;
  color: #333;
}

.map-error {
  color: #e74c3c;
  font-size: 11px;
  max-width: 200px;
  word-wrap: break-word;
}
</style>
