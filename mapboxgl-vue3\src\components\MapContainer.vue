<template>
  <div class="map-container">
    <div id="mapbox-map" class="map"></div>
    <div class="map-overlay">
      <div class="map-info">
        <span class="map-status">{{ mapStatus }}</span>
        <span v-if="error" class="map-error">{{ error }}</span>
        <div class="terrain-info">
          <div class="terrain-item">
            <span class="terrain-label">地形夸张度:</span>
            <span class="terrain-value">{{ currentExaggeration.toFixed(2) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import mapboxgl from 'mapbox-gl'
import { mapboxConfig, validateConfig, getTerrainSourceConfig } from '@/config/mapbox'
import { useDataLoader } from '@/composables/useDataLoader'

// 响应式状态
const mapStatus = ref('地图初始化中...')
const error = ref<string | null>(null)
const mapInstance = ref<mapboxgl.Map | null>(null)

// 数据加载器
const { loadAllData, trackData } = useDataLoader()

// 地形方差图相关
const elevationImage = ref<HTMLImageElement | null>(null)
const elevationContext = ref<CanvasRenderingContext2D | null>(null)
const currentExaggeration = ref(1.35)

// 初始化地形方差图
function initializeElevationImage() {
  const img = new Image()
  elevationImage.value = img

  img.onload = () => {
    // 创建 Canvas 来处理地形变化图像
    const canvas = document.createElement('canvas')
    canvas.width = img.width
    canvas.height = img.height
    const gc = canvas.getContext('2d')
    if (gc) {
      gc.drawImage(img, 0, 0)
      elevationContext.value = gc
      console.log('地形方差图加载完成:', { width: img.width, height: img.height })
    }
  }

  img.onerror = () => {
    console.error('地形方差图加载失败')
  }

  img.src = '/dem_std_zoom_4_512.png'
}

// 更新地形夸张度
function updateTerrain(map: mapboxgl.Map) {
  const center = map.getCenter()
  let exaggeration = 1.35 // 默认地形夸张度

  // 根据地形变化图像调整夸张度
  if (elevationContext.value && elevationImage.value) {
    try {
      const size = elevationImage.value.width
      const xy = mapboxgl.MercatorCoordinate.fromLngLat([center.lng, center.lat])

      // 将墨卡托坐标映射到图像像素坐标
      const pixelX = Math.floor(xy.x * size)
      const pixelY = Math.floor(xy.y * size)

      // 确保坐标在有效范围内
      const clampedX = Math.max(0, Math.min(pixelX, size - 1))
      const clampedY = Math.max(0, Math.min(pixelY, size - 1))

      // 获取当前位置的地形变化值（0-1）
      const imageData = elevationContext.value.getImageData(clampedX, clampedY, 1, 1)
      const variance = imageData.data[2] / 255

      // 根据地形变化调整夸张度
      // variance = 0: 地形变化最大（山地）-> 最大夸张度
      // variance = 1: 地形变化最小（平原）-> 最小夸张度
      exaggeration = 1.0 + (1 - variance) * 0.7

      console.log('地形夸张度更新:', {
        center: [center.lng, center.lat],
        variance: variance.toFixed(3),
        exaggeration: exaggeration.toFixed(3)
      })

    } catch (error) {
      console.error('地形夸张度计算失败:', error)
    }
  }

  currentExaggeration.value = exaggeration

  // 更新地形设置
  try {
    map.setTerrain({
      source: 'mapbox-dem',
      exaggeration: [
        'interpolate',
        ['linear'],
        ['zoom'],
        8, exaggeration + 0.2,
        12, exaggeration,
        16, exaggeration * 0.8
      ]
    } as mapboxgl.TerrainSpecification)
  } catch (error) {
    console.error('地形设置失败:', error)
  }
}

onMounted(async () => {
  try {
    // 验证配置
    if (!validateConfig()) {
      throw new Error('Mapbox配置验证失败，请检查环境变量')
    }

    // 设置访问令牌
    mapboxgl.accessToken = mapboxConfig.accessToken

    // 初始化地形方差图
    initializeElevationImage()

    mapStatus.value = '创建地图实例...'

    // 创建地图实例
    const map = new mapboxgl.Map({
      container: 'mapbox-map',
      style: mapboxConfig.style,
      center: [24.9384, 60.1699], // 默认中心点 (赫尔辛基)
      zoom: 10,
      pitch: 45,
      bearing: 0,
      antialias: true
    })

    mapInstance.value = map

    // 地图加载完成事件
    map.on('load', async () => {
      try {
        mapStatus.value = '配置地形...'

        // 添加地形数据源
        map.addSource('mapbox-dem', getTerrainSourceConfig() as mapboxgl.RasterDEMSourceSpecification)

        // 初始地形更新
        updateTerrain(map)

        mapStatus.value = '加载轨迹数据...'

        // 加载数据
        await loadAllData()

        // 如果有轨迹数据，添加到地图并调整视图
        if (trackData.value) {
          // 添加轨迹数据源
          map.addSource('track-data', {
            type: 'geojson',
            data: {
              type: 'LineString',
              coordinates: trackData.value.points.map(point => [point.lng, point.lat, point.elevation || 0])
            }
          })

          // 添加轨迹线图层
          map.addLayer({
            id: 'track-line',
            type: 'line',
            source: 'track-data',
            layout: {
              'line-join': 'round',
              'line-cap': 'round'
            },
            paint: {
              'line-color': '#ff6b35',
              'line-width': 4,
              'line-opacity': 0.8
            }
          })

          // 添加轨迹起点标记
          const startPoint = trackData.value.points[0]
          if (startPoint) {
            map.addSource('start-point', {
              type: 'geojson',
              data: {
                type: 'Point',
                coordinates: [startPoint.lng, startPoint.lat]
              }
            })

            map.addLayer({
              id: 'start-marker',
              type: 'circle',
              source: 'start-point',
              paint: {
                'circle-radius': 8,
                'circle-color': '#00ff00',
                'circle-stroke-color': '#ffffff',
                'circle-stroke-width': 2
              }
            })
          }

          // 添加轨迹终点标记
          const endPoint = trackData.value.points[trackData.value.points.length - 1]
          if (endPoint) {
            map.addSource('end-point', {
              type: 'geojson',
              data: {
                type: 'Point',
                coordinates: [endPoint.lng, endPoint.lat]
              }
            })

            map.addLayer({
              id: 'end-marker',
              type: 'circle',
              source: 'end-point',
              paint: {
                'circle-radius': 8,
                'circle-color': '#ff0000',
                'circle-stroke-color': '#ffffff',
                'circle-stroke-width': 2
              }
            })
          }

          // 调整视图到轨迹范围
          const bounds = new mapboxgl.LngLatBounds()
          trackData.value.points.forEach(point => {
            bounds.extend([point.lng, point.lat])
          })

          map.fitBounds(bounds, {
            padding: 50,
            duration: 2000
          })

          console.log(`轨迹已添加到地图，共 ${trackData.value.points.length} 个点`)
        }

        // 设置地图移动监听器
        map.on('moveend', () => updateTerrain(map))
        map.on('zoomend', () => updateTerrain(map))

        mapStatus.value = '地图已就绪'
        console.log('Mapbox地图初始化完成')

      } catch (err) {
        console.error('地图配置失败:', err)
        error.value = err instanceof Error ? err.message : '地图配置失败'
        mapStatus.value = '地图配置失败'
      }
    })

    // 地图错误事件
    map.on('error', (e) => {
      console.error('Mapbox地图错误:', e.error)
      error.value = e.error.message
      mapStatus.value = '地图加载失败'
    })

  } catch (err) {
    console.error('地图初始化失败:', err)
    error.value = err instanceof Error ? err.message : '地图初始化失败'
    mapStatus.value = '地图初始化失败'
  }
})

onUnmounted(() => {
  // 清理地图实例
  if (mapInstance.value) {
    mapInstance.value.remove()
    mapInstance.value = null
  }
})
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.map {
  width: 100%;
  height: 100%;
}

.map-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  color: #666666;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.map-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.map-status {
  font-weight: 500;
  color: #333;
}

.map-error {
  color: #e74c3c;
  font-size: 11px;
  max-width: 200px;
  word-wrap: break-word;
}

.terrain-info {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e0e0e0;
}

.terrain-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 11px;
}

.terrain-label {
  color: #666;
  font-weight: 500;
}

.terrain-value {
  color: #333;
  font-weight: 600;
  background: rgba(0, 123, 255, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
}
</style>
