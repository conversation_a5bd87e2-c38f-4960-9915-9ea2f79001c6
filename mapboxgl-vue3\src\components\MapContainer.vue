<template>
  <div class="map-container">
    <div id="mapbox-map" class="map"></div>
    <div class="map-overlay">
      <div class="map-info">
        <span class="map-status">地图加载中...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

const mapStatus = ref('地图加载中...')

onMounted(() => {
  // 这里将来会初始化Mapbox地图
  console.log('MapContainer mounted, ready for Mapbox integration')
  
  // 模拟地图加载完成
  setTimeout(() => {
    mapStatus.value = '地图已就绪'
  }, 2000)
})
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.map {
  width: 100%;
  height: 100%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  color: #666666;
}

.map-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.map-status {
  font-weight: 500;
}
</style>
