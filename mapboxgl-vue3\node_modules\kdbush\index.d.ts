export default class KDBush {
    /**
     * Creates an index from raw `<PERSON><PERSON>yBuffer` data.
     * @param {ArrayBuffer} data
     */
    static from(data: ArrayBuffer): KDBush;
    /**
     * Creates an index that will hold a given number of items.
     * @param {number} numItems
     * @param {number} [nodeSize=64] Size of the KD-tree node (64 by default).
     * @param {TypedArrayConstructor} [ArrayType=Float64Array] The array type used for coordinates storage (`Float64Array` by default).
     * @param {ArrayBuffer} [data] (For internal use only)
     */
    constructor(numItems: number, nodeSize?: number | undefined, ArrayType?: TypedArrayConstructor | undefined, data?: ArrayBuffer | undefined);
    numItems: number;
    nodeSize: number;
    ArrayType: TypedArrayConstructor;
    IndexArrayType: Uint16ArrayConstructor | Uint32ArrayConstructor;
    data: ArrayBuffer;
    ids: Uint16Array | Uint32Array;
    coords: Int8Array | Uint8Array | Uint8ClampedArray | Int16Array | Uint16Array | Int32Array | Uint32Array | Float32Array | Float64Array;
    _pos: number;
    _finished: boolean;
    /**
     * Add a point to the index.
     * @param {number} x
     * @param {number} y
     * @returns {number} An incremental index associated with the added item (starting from `0`).
     */
    add(x: number, y: number): number;
    /**
     * Perform indexing of the added points.
     */
    finish(): KDBush;
    /**
     * Search the index for items within a given bounding box.
     * @param {number} minX
     * @param {number} minY
     * @param {number} maxX
     * @param {number} maxY
     * @returns {number[]} An array of indices correponding to the found items.
     */
    range(minX: number, minY: number, maxX: number, maxY: number): number[];
    /**
     * Search the index for items within a given radius.
     * @param {number} qx
     * @param {number} qy
     * @param {number} r Query radius.
     * @returns {number[]} An array of indices correponding to the found items.
     */
    within(qx: number, qy: number, r: number): number[];
}
export type TypedArrayConstructor = Int8ArrayConstructor | Uint8ArrayConstructor | Uint8ClampedArrayConstructor | Int16ArrayConstructor | Uint16ArrayConstructor | Int32ArrayConstructor | Uint32ArrayConstructor | Float32ArrayConstructor | Float64ArrayConstructor;
