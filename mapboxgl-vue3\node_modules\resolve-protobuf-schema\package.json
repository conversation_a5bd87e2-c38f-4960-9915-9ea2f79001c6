{"name": "resolve-protobuf-schema", "version": "2.1.0", "description": "Read a protobuf schema from the disk, parse it and resolve all imports", "main": "index.js", "dependencies": {"protocol-buffers-schema": "^3.3.1"}, "devDependencies": {"tape": "^3.0.0"}, "scripts": {"test": "tape test/index.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/resolve-protobuf-schema.git"}, "author": "<PERSON> (@mafintosh)", "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/resolve-protobuf-schema/issues"}, "homepage": "https://github.com/mafintosh/resolve-protobuf-schema"}